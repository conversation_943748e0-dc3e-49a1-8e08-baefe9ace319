import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMindBookStore } from '../core/state/MindBookStore';

/**
 * InitialView Component
 *
 * This is the initial view of the application.
 *
 * NOTE: GovernanceBox is now rendered via AppRefactored -> MindBook, not here.
 * All mindmap operations now use the MindSheet framework.
 */
interface InitialViewProps {
  isContextPanelOpen?: boolean;
  showGovernanceChat?: boolean;
  isGovernanceChatMinimized?: boolean;
  isGovernanceChatFullyCollapsed?: boolean;
  setShowGovernanceChat?: (show: boolean) => void;
  setIsGovernanceChatMinimized?: (minimized: boolean) => void;
  setIsGovernanceChatFullyCollapsed?: (collapsed: boolean) => void;
}

const InitialView: React.FC<InitialViewProps> = ({
  isContextPanelOpen = false,
  // These props are now ignored since governance is handled by AppRefactored
  showGovernanceChat: propShowGovernanceChat,
  isGovernanceChatMinimized: propIsGovernanceChatMinimized,
  isGovernanceChatFullyCollapsed: propIsGovernanceChatFullyCollapsed,
  setShowGovernanceChat: propSetShowGovernanceChat,
  setIsGovernanceChatMinimized: propSetIsGovernanceChatMinimized,
  setIsGovernanceChatFullyCollapsed: propSetIsGovernanceChatFullyCollapsed
}) => {
  // Legacy mindmap event handlers removed - all mindmap operations now use MindSheet framework

  // Legacy chat action handlers removed - all actions now handled by MindBook framework

  return (
    <div className="initial-view">
      {/* Governance box is now rendered by AppRefactored -> MindBook */}
      {/* No longer rendering GovernanceBoxPositioned here to avoid conflicts */}

      {/* Legacy MindMap Component removed - All mindmap operations now use MindSheet framework */}

      {/* Controls Bar - Empty since governance is handled elsewhere */}
      <div className="control-buttons-bar governance-controls">
        {/* The governance button that was here has been removed */}
      </div>
    </div>
  );
};

export default InitialView;
