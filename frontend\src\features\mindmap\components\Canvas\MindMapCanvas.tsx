/**
 * MindMapCanvas.tsx
 *
 * Canvas component for rendering the mind map.
 * Uses react-konva for rendering.
 */

import React, { useRef, useEffect, useState } from 'react';
import { Layer, Group } from 'react-konva';
import NodeComponent from './NodeComponent';
import ConnectionComponent from './ConnectionComponent';
import StageCompatWrapper from '../../../../components/MindMap/components/Canvas/StageCompatWrapper';
import '../../../../core/services/KeyboardManager'; // Import to ensure it's initialized
import './MindMapCanvas.css';
import { mindMapGovernance } from '../../../../core/governance/MindMapGovernance';
import RegistrationManager, { EventType } from '../../../../core/services/RegistrationManager';
import { useStoreRegistry } from '../../../../core/context/StoreContexts';

// Log window dimensions to help with debugging
console.log('MindMapCanvas: Window dimensions:', {
  width: window.innerWidth,
  height: window.innerHeight
});

// Cast Konva components to any to bypass TypeScript errors
const KonvaLayer = Layer as any;
const KonvaGroup = Group as any;

interface MindMapCanvasProps {
  width: number;
  height: number;
  sheetId: string; // Sheet ID for store access
  store?: any; // Optional store prop for direct usage (legacy support)
}

const MindMapCanvas: React.FC<MindMapCanvasProps> = ({ width, height, sheetId, store: propStore }) => {
  const stageRef = useRef<any>(null);

  // Get store registry from context (for wrapper functionality)
  const storeRegistry = useStoreRegistry();

  // Use prop store if provided, otherwise get from registry
  const store = propStore || storeRegistry.getStore(sheetId);
  const storeRef = useRef<any>(store);

  // Store the store reference in a ref to avoid re-renders
  if (store !== storeRef.current) {
    storeRef.current = store;
  }

  // State to track if the store is ready (from wrapper functionality)
  const [storeReady, setStoreReady] = useState(false);

  // Use state to store values from the store and subscribe to changes
  const [storeState, setStoreState] = useState(() => {
    return storeRef.current ? storeRef.current.getState() : {
      nodes: {},
      connections: [],
      position: { x: 0, y: 0 },
      scale: 1,
      selectedNodeId: null,
      rootNodeId: null
    };
  });

  // Store initialization and event handling (consolidated from wrapper)
  useEffect(() => {
    console.log('MindMapCanvas: Mounted for sheet:', sheetId);

    try {
      // Ensure the store exists for this sheet
      if (!propStore && !storeRegistry.hasStore(sheetId)) {
        console.log('MindMapCanvas: Creating new store for sheet:', sheetId);
        // Store is automatically created by getStore call above
      } else {
        console.log('MindMapCanvas: Using existing store for sheet:', sheetId);
      }

      // Check if the store has nodes
      const storeState = store.getState();
      const hasNodes = Object.keys(storeState.nodes).length > 0;

      console.log('MindMapCanvas: Store has nodes:', hasNodes, 'count:', Object.keys(storeState.nodes).length);

      // Register the canvas mounted event
      RegistrationManager.registerEvent(EventType.CANVAS_MOUNTED, {
        sheetId: sheetId,
        hasNodes
      });

      // Mark the store as ready
      setStoreReady(true);

      // Set up a listener for node selection events from the store
      const unsubscribeFromNodeSelection = store.subscribe(
        (state: any) => state.selectedNodeId,
        (selectedNodeId: any) => {
          if (selectedNodeId) {
            console.log('MindMapCanvas: Node selected in store, dispatching event:', selectedNodeId);
            try {
              const event = new CustomEvent('mindback:node_selected', {
                detail: {
                  nodeId: selectedNodeId,
                  sheetId: sheetId
                }
              });
              document.dispatchEvent(event);
            } catch (error) {
              console.error('MindMapCanvas: Error dispatching node_selected event:', error);
            }
          }
        }
      );

      // Set up a listener for node changes in the store
      const unsubscribeFromNodeChanges = store.subscribe(
        (state: any) => Object.keys(state.nodes).length,
        (nodeCount: number, prevNodeCount: number) => {
          if (nodeCount > prevNodeCount) {
            console.log('MindMapCanvas: Node added to store, dispatching refresh event');
            try {
              // Get the most recently added node
              const nodes = store.getState().nodes;
              const nodeIds = Object.keys(nodes);
              const latestNodeId = nodeIds[nodeIds.length - 1];

              // Dispatch a refresh event
              setTimeout(() => {
                const refreshEvent = new CustomEvent('mindback:refresh_canvas', {
                  detail: {
                    nodeId: latestNodeId,
                    sheetId: sheetId
                  }
                });
                document.dispatchEvent(refreshEvent);
                console.log('MindMapCanvas: Dispatched refresh_canvas event after adding node:', latestNodeId);
              }, 10);
            } catch (error) {
              console.error('MindMapCanvas: Error dispatching refresh event:', error);
            }
          }
        }
      );

      return () => {
        console.log('MindMapCanvas: Unmounted for sheet:', sheetId);

        // Unsubscribe from store subscriptions
        if (unsubscribeFromNodeSelection) {
          unsubscribeFromNodeSelection();
        }
        if (unsubscribeFromNodeChanges) {
          unsubscribeFromNodeChanges();
        }
      };
    } catch (error) {
      console.error('MindMapCanvas: Error initializing store for sheet:', sheetId, error);
      setStoreReady(false);
    }
  }, [sheetId, storeRegistry, store, propStore]);

  // Subscribe to store changes for UI updates
  useEffect(() => {
    if (storeRef.current) {
      console.log(`MindMapCanvas: Subscribing to store changes for sheet: ${sheetId}`);

      // Initial state update
      setStoreState(storeRef.current.getState());

      // Subscribe to store changes
      const unsubscribe = storeRef.current.subscribe((state: any) => {
        console.log(`MindMapCanvas: Store updated for sheet ${sheetId}:`, {
          nodeCount: Object.keys(state.nodes || {}).length,
          connectionCount: (state.connections || []).length,
          position: state.position,
          scale: state.scale
        });
        setStoreState(state);
      });

      return () => {
        if (unsubscribe) unsubscribe();
      };
    }
  }, [sheetId]);

  // Log the sheet ID and store to help with debugging
  useEffect(() => {
    if (storeRef.current) {
      console.log(`MindMapCanvas: Using sheet-specific store for sheet: ${sheetId}`);
      console.log(`MindMapCanvas: Store has ${Object.keys(storeRef.current.getState().nodes).length} nodes`);
    }
  }, [sheetId]);

  // Extract values from store state
  const nodes = storeState.nodes || {};
  const connections = storeState.connections || [];
  const position = storeState.position || { x: 0, y: 0 };
  const scale = storeState.scale || 1;
  const selectedNodeId = storeState.selectedNodeId;
  
  // Get action functions from the store (these don't change)
  const selectNode = storeRef.current?.getState().selectNode || (() => {});
  const setPosition = storeRef.current?.getState().setPosition || (() => {});
  const setScale = storeRef.current?.getState().setScale || (() => {});
  const updateNode = storeRef.current?.getState().updateNode || (() => {});

  // Focus the stage when a node is selected, but only if this sheet is active
  useEffect(() => {
    if (selectedNodeId && stageRef.current) {
      console.log('Node selected in sheet:', sheetId, 'nodeId:', selectedNodeId);

      // Check if this sheet is active before focusing
      const isActive = document.querySelector(`[data-sheet-id="${sheetId}"]`)?.classList.contains('active');

      if (!isActive) {
        console.log('Not focusing stage because sheet is not active:', sheetId);
        return;
      }

      // Focus with a slight delay to ensure the DOM is ready
      setTimeout(() => {
        if (stageRef.current) {
          const container = stageRef.current.getStage().container();
          container.focus();
          console.log('Stage focused for sheet:', sheetId, 'success:', document.activeElement === container);

          // DISABLED: Competing centering system - let UnifiedLayoutManager handle centering
          console.log('MindMapCanvas: 🚫 Auto-centering DISABLED - UnifiedLayoutManager handles positioning');

          // Force a re-render to ensure the node is visually selected
          setTimeout(() => {
            try {
              // Dispatch a custom event to notify that a node was selected
              const event = new CustomEvent('mindback:node_selected', {
                detail: {
                  nodeId: selectedNodeId,
                  sheetId: sheetId
                }
              });
              document.dispatchEvent(event);
            } catch (error) {
              console.error('Error updating node selection state:', error);
            }
          }, 50);
        }
      }, 100);
    }
  }, [selectedNodeId, nodes, scale, position, sheetId, setPosition]);

  // We no longer need a global Tab key handler here
  // The KeyboardManager service now handles all keyboard events centrally
  // This ensures that only the active mindsheet receives keyboard events

  // Public method to focus the stage
  const focusStage = () => {
    if (stageRef.current) {
      const container = stageRef.current.getStage().container();
      container.focus();
      console.log('Stage focused via public method');
      return true;
    }
    return false;
  };

  // Focus the stage on mount
  useEffect(() => {
    // Only proceed if this sheet is active
    const isActive = document.querySelector(`[data-sheet-id="${sheetId}"]`)?.classList.contains('active');
    if (!isActive) {
      console.log('MindMapCanvas: Not focusing because sheet is not active:', sheetId);
      return;
    }

    // Focus with a delay to ensure the DOM is ready
    const timer = setTimeout(() => {
      focusStage();
    }, 100);

    return () => clearTimeout(timer);
  }, [sheetId]);

  // Listen for the refresh_canvas event only
  useEffect(() => {
    // Handler for refresh_canvas event
    const handleRefreshCanvas = (event: any) => {
      // Check if this event is for this sheet or for all sheets
      if (event.detail.sheetId === sheetId || event.detail.sheetId === 'current') {
        console.log('MindMapCanvas: Received refresh_canvas event for sheet:', sheetId);

        // Force a re-render by updating a state variable
        setTimeout(() => {
          if (storeRef.current) {
            try {
              // Get the current state
              const currentState = storeRef.current.getState();

              // Log the current state before refresh
              console.log('MindMapCanvas: Current state before refresh:', {
                nodeCount: Object.keys(currentState.nodes || {}).length,
                connectionCount: (currentState.connections || []).length,
                selectedNodeId: currentState.selectedNodeId,
                rootNodeId: currentState.rootNodeId
              });

              // Check if we have a specific node ID in the event
              if (event.detail.nodeId) {
                console.log('MindMapCanvas: Refresh event includes node ID:', event.detail.nodeId);

                // Check if this node exists in our store
                const node = currentState.nodes[event.detail.nodeId];
                if (node) {
                  console.log('MindMapCanvas: Node found in store:', node.id, node.text);

                  // Select this node to make it visible
                  currentState.selectNode(node.id);
                } else {
                  console.log('MindMapCanvas: Node not found in store:', event.detail.nodeId);
                }
              }

              // Also update the layout to ensure proper positioning
              if (currentState.updateLayout) {
                currentState.updateLayout('leftToRight');
                console.log('MindMapCanvas: Updated layout after refresh event');
              } else {
                console.warn('MindMapCanvas: updateLayout function not found in store');
              }
            } catch (error) {
              console.error('MindMapCanvas: Error refreshing canvas:', error);
            }
          }
        }, 50);
      }
    };

    // Add event listener
    document.addEventListener('mindback:refresh_canvas', handleRefreshCanvas);

    // Clean up
    return () => {
      document.removeEventListener('mindback:refresh_canvas', handleRefreshCanvas);
    };
  }, [sheetId]);

  // Handle center view events (consolidated from wrapper)
  useEffect(() => {
    const handleCenterView = (event: CustomEvent) => {
      if (event.detail?.sheetId === sheetId) {
        console.log('MindMapCanvas: Received center view event for sheet:', sheetId);

        // CONSOLIDATED: Route all centering through UnifiedLayoutManager
        console.log('[MindMapCanvas] 🎯 ROUTING CENTER VIEW to UnifiedLayoutManager');

        // Import UnifiedLayoutManager dynamically to avoid circular dependencies
        import('../../../../core/layout/UnifiedLayoutManager').then(({ UnifiedLayoutManager }) => {
          const layoutManager = UnifiedLayoutManager.getInstance();

          // FIXED: Use requestLayoutChange instead of non-existent centerViewport method
          // This will trigger a layout update which includes automatic centering
          layoutManager.requestLayoutChange({
            strategy: 'leftToRight', // Use current or default strategy
            sheetId: sheetId,
            requestOrigin: 'user',
            reason: 'Center view requested'
          }).then((response) => {
            if (response.success) {
              console.log('[MindMapCanvas] ✅ Center view completed via layout update');
            } else {
              console.warn('[MindMapCanvas] ⚠️ Center view failed:', response.reason);
            }
          }).catch(error => {
            console.error('[MindMapCanvas] Error centering view:', error);
          });
        }).catch(error => {
          console.error('[MindMapCanvas] Error importing UnifiedLayoutManager:', error);
        });
      }
    };

    // Add event listener
    document.addEventListener('mindback:center_view', handleCenterView as EventListener);

    // Clean up
    return () => {
      document.removeEventListener('mindback:center_view', handleCenterView as EventListener);
    };
  }, [sheetId, store]);

  // Handle stage drag start
  const handleDragStart = (e: any) => {
    // Only handle stage drag if no node is being dragged
    if (e.target === stageRef.current.getStage()) {
      console.log('Stage drag started');
    }
  };

  // Handle stage drag move
  const handleDragMove = (e: any) => {
    // Only handle stage drag if no node is being dragged
    if (e.target === stageRef.current.getStage()) {
      // Update position in real-time for smoother dragging
      const newPos = stageRef.current.position();
      setPosition(newPos);
    }
  };

  // Handle stage drag end
  const handleDragEnd = (e: any) => {
    // Only handle stage drag if no node is being dragged
    if (e.target === stageRef.current.getStage()) {
      console.log('Stage drag ended');
      const newPos = stageRef.current.position();
      setPosition(newPos);
    }
  };

  // Handle wheel zoom
  const handleWheel = (e: any) => {
    // Prevent default scrolling
    e.evt.preventDefault();

    // Get current sheet for governance validation
    const currentSheet = document.querySelector('.mind-sheet.active');
    const sheetId = currentSheet?.getAttribute('data-sheet-id');
    
    if (!sheetId) return;

    const stage = e.target.getStage();
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();

    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    // Zoom direction
    const direction = e.evt.deltaY > 0 ? -1 : 1;

    // Zoom factor
    const zoomFactor = 1.1;
    const newScale = direction > 0 ? oldScale * zoomFactor : oldScale / zoomFactor;

    // Validate scale change with governance
    if (!mindMapGovernance.validateViewportChange(sheetId, { scale: newScale })) {
      console.log('MindMapCanvas: Scale change rejected by governance');
      return;
    }

    // Apply the new scale
    setScale(newScale);

    // Calculate new position
    const newPos = {
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    };

    // Validate and apply position change
    if (mindMapGovernance.validateViewportChange(sheetId, { position: newPos })) {
      setPosition(newPos);
    }

    console.log('MindMapCanvas: Zoom applied - scale:', newScale, 'position:', newPos);
  };

  // Handle keyboard shortcuts for the canvas
  // Tab key is now handled by the KeyboardManager service
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Prevent default for handled keys
    const handledKeys = ['Tab', 'Enter', 'Delete', 'Backspace', 'Escape'];
    if (handledKeys.includes(e.key)) {
      e.preventDefault();
    }

    console.log('MindMapCanvas: Key pressed:', e.key, 'for sheet:', sheetId);

    switch (e.key) {
      case 'Tab':
        if (selectedNodeId && nodes[selectedNodeId]) {
          console.log('MindMapCanvas: Tab key - adding child node');

          const parentNode = nodes[selectedNodeId];

          // Calculate new child node position relative to parent
          // Account for current viewport position to ensure visibility
          const stagePos = stageRef.current?.position() || { x: 0, y: 0 };
          const viewportWidth = window.innerWidth;
          const viewportHeight = window.innerHeight;

          // Calculate position in world coordinates
          let newX = parentNode.x + 250;
          let newY = parentNode.y;

          // Check if the new position would be visible on screen
          const screenX = newX + stagePos.x;
          const screenY = newY + stagePos.y;

          // If off-screen, adjust position
          if (screenX > viewportWidth - 250) {
            newX = parentNode.x - 250; // Try left side
            if (newX + stagePos.x < 50) {
              newX = parentNode.x; // Keep same X
              newY = parentNode.y + 150; // Move down
            }
          }

          console.log(`MindMapCanvas: Tab positioning - parent: (${parentNode.x}, ${parentNode.y}), stage: (${stagePos.x}, ${stagePos.y}), new: (${newX}, ${newY}), screen: (${screenX}, ${screenY})`);

          // Generate proper node path for the child
          const parentPath = parentNode.metadata?.nodePath || '1.0';

          // Count existing children to determine the next child number
          const existingChildren = connections.filter(conn => conn.from === selectedNodeId);
          const childNumber = existingChildren.length + 1;

          // Create the child node path
          const childPath = parentPath.endsWith('.0')
            ? `${parentPath.slice(0, -2)}.${childNumber}`
            : `${parentPath}.${childNumber}`;

          console.log(`MindMapCanvas: Creating child node with path: ${childPath}`);

          // Add child node with proper metadata
          const childId = storeRef.current?.getState().addNode(
            selectedNodeId,
            'New Node',
            newX,
            newY,
            {
              metadata: {
                nodePath: childPath,
                isManuallyAdded: true,
                createdAt: Date.now()
              }
            }
          );

          if (childId) {
            // Select the new child node if not Shift+Tab
            if (!e.shiftKey) {
              selectNode(childId);
              console.log('MindMapCanvas: Child node added and selected:', childId);
            } else {
              console.log('MindMapCanvas: Child node added but parent remains selected (Shift+Tab)');
            }

            // Register the node creation event
            RegistrationManager.registerEvent(EventType.NODE_CREATED, {
              id: childId,
              parentId: selectedNodeId,
              nodePath: childPath
            });
          }
        }
        break;
        
      case 'Enter':
        if (selectedNodeId && nodes[selectedNodeId]) {
          console.log('MindMapCanvas: Enter key - adding sibling node');
          
          const currentNode = nodes[selectedNodeId];
          
          // Find the parent of the current node
          const parentConnection = connections.find(conn => conn.to === selectedNodeId);
          const parentId = parentConnection ? parentConnection.from : null;
          
          if (parentId && nodes[parentId]) {
            const parentNode = nodes[parentId];
            
            // Calculate sibling position
            const newX = currentNode.x;
            const newY = currentNode.y + 100; // Position below current node
            
            // Add sibling node
            const siblingId = storeRef.current?.getState().addNode(
              parentId,
              'New Node',
              newX,
              newY
            );
            
            if (siblingId) {
              // Select the new sibling node
              selectNode(siblingId);
              
              console.log('MindMapCanvas: Sibling node added and selected:', siblingId);
            }
          } else {
            console.log('MindMapCanvas: No parent found for sibling creation');
          }
        }
        break;
    }
  };

  // Render loading message if no nodes
  if (!nodes || Object.keys(nodes).length === 0) {
    console.log('MindMapCanvas: No nodes found, showing loading state for sheet:', sheetId);
    return (
      <div className="loading-canvas">
        <div className="initializing-mindmap">
          <div className="loading-spinner"></div>
          <p>Initializing canvas for sheet {sheetId}...</p>
          <button
            onClick={() => {
              console.log('Forcing sheet-specific store update for sheet:', sheetId);

              // Check if we have saved state in the MindBookStore
              try {
                // Import the functions directly to avoid circular dependencies
                import('../../../../core/state/MindBookStore').then(({ useMindBookStore }) => {
                  const mindBookStore = useMindBookStore.getState();
                  const savedState = mindBookStore.getSheetState(sheetId);

                  if (savedState && savedState.nodes && Object.keys(savedState.nodes).length > 0) {
                    console.log('MindMapCanvas: Found saved state in MindBookStore, applying it');

                    // Apply the saved state to the store
                    const storeState = storeRef.current.getState();

                    // Apply nodes
                    Object.entries(savedState.nodes).forEach(([nodeId, node]) => {
                      storeState.updateNode(nodeId, node as any);
                    });

                    // Apply connections
                    if (savedState.connections && Array.isArray(savedState.connections)) {
                      savedState.connections.forEach((conn: any) => {
                        if (conn.from && conn.to) {
                          storeState.addConnection(conn.from, conn.to, {
                            color: conn.color,
                            width: conn.width,
                            style: conn.style
                          });
                        }
                      });
                    }

                    // Apply position and scale
                    if (savedState.position) {
                      storeState.setPosition(savedState.position);
                    }

                    if (savedState.scale) {
                      storeState.setScale(savedState.scale);
                    }

                    // Apply root node ID
                    if (savedState.rootNodeId) {
                      storeState.rootNodeId = savedState.rootNodeId;
                    }
                  }
                }).catch(error => {
                  console.error('MindMapCanvas: Error importing MindBookStore:', error);
                });
              } catch (error) {
                console.error('MindMapCanvas: Error applying saved state:', error);
              }

              // Force the sheet-specific store to update its layout
              if (storeRef.current) {
                const { updateLayout } = storeRef.current.getState();
                if (updateLayout) {
                  updateLayout('leftToRight');
                }
              }
            }}
            style={{ marginTop: '10px', padding: '5px 10px' }}
          >
            Refresh Canvas
          </button>
        </div>
      </div>
    );
  }

  console.log('MindMapCanvas: Rendering with nodes:', Object.keys(nodes).length);

  // Use the position from the store for the Stage
  // The store position represents where the stage should be to center the content
  console.log(`MindMapCanvas: Using store position (${position.x}, ${position.y}) for stage - width: ${width}, height: ${height}`);

  return (
    <div
      className="mindmap-canvas-container"
      style={{ width: '100%', height: '100%' }}
      key={`canvas-${sheetId}-${storeReady}`} // Force re-render when store changes
    >
      <StageCompatWrapper
        ref={stageRef}
        width={width}
        height={height}
        draggable
        x={position.x}
        y={position.y}
        scaleX={scale}
        scaleY={scale}
        onDragStart={handleDragStart}
        onDragMove={handleDragMove}
        onDragEnd={handleDragEnd}
        onWheel={handleWheel}
        onKeyDown={handleKeyDown}
        onClick={(e) => {
          // Only handle stage clicks, not node clicks
          if (e.target === stageRef.current.getStage()) {
            // Focus the stage on click
            if (stageRef.current) {
              stageRef.current.getStage().container().focus();
              console.log('Stage clicked and focused');

              // Deselect any selected node when clicking on empty space
              if (selectedNodeId) {
                selectNode(null);
                console.log('Deselected node on stage click');
              }
            }
          }
        }}
        tabIndex={0} // Make the stage focusable
      >
      <KonvaLayer>
        {/* Render connections */}
        <KonvaGroup>
          {connections.map((connection: any) => (
            <ConnectionComponent
              key={connection.id}
              connection={connection}
              fromNode={nodes[connection.from]}
              toNode={nodes[connection.to]}
            />
          ))}
        </KonvaGroup>

        {/* Render nodes */}
        <KonvaGroup>
          {Object.values(nodes).map((node: any) => (
            <NodeComponent
              key={node.id}
              node={node}
              isSelected={node.id === selectedNodeId}
              onClick={() => selectNode(node.id)}
              updateNode={updateNode}
              selectNode={selectNode}
            />
          ))}
        </KonvaGroup>
      </KonvaLayer>
    </StageCompatWrapper>
    </div>
  );
};

export default MindMapCanvas;
