/**
 * GovernanceBoxPositioned.tsx
 *
 * A standalone version of the GovernanceBox component that uses the positioning system.
 * This component includes all the functionality of the original GovernanceChatDialog
 * but uses the positioning system for layout management.
 */

import React, { useState, useEffect, useRef } from 'react';
import { Rnd } from 'react-rnd';
import { useUIElement, ZIndexLayer } from '../../core/positioning';
import './styles.css';
import MessageList from './MessageList';
import MessageInput from './components/MessageInput';
import { useChat } from './hooks/useChat';
import { ModelSelector } from './components/ModelSelector';
import useChatStore from './state/ChatStore';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import { useMindBookStore } from '../../core/state/MindBookStore';
// Legacy MindMapStore import removed - using sheet-specific stores via MindBook framework
import { useMindMapStoreManager } from '../../core/state/MindMapStoreManager';
import { getMindMapStore } from '../../core/state/MindMapStoreFactory';
import { MindSheetContentType } from '../../core/state/StoreTypes';
import { useChatForkStore } from '../../components/ChatFork/ChatForkStore';
import { v4 as uuidv4 } from 'uuid';
import { mindMapGovernance } from '../../core/governance/MindMapGovernance';
import { initializeMindMapSheet } from '../../core/services/MindSheetService';

// Material UI imports for header icons
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import MinimizeIcon from '@mui/icons-material/Minimize';
import RefreshIcon from '@mui/icons-material/Refresh';

// Default position and size constants - centered in the viewport
const DEFAULT_POSITION = {
  x: Math.max(100, window.innerWidth / 2 - 450), // Adjusted for wider box (900/2 = 450)
  y: Math.max(50, window.innerHeight / 2 - 300)
};
const DEFAULT_SIZE = { width: 900, height: 600 };
const COLLAPSED_SIZE = { width: 900, height: 48 }; // Just enough for the header

interface GovernanceBoxPositionedProps {
  isOpen: boolean;
  isCollapsed?: boolean;
  onClose: () => void;
  onCollapse?: () => void;
  onAction?: (action: any) => void;
  isContextPanelOpen?: boolean;
}

const GovernanceBoxPositioned: React.FC<GovernanceBoxPositionedProps> = ({
  isOpen,
  isCollapsed = false,
  onClose,
  onCollapse,
  onAction,
  isContextPanelOpen = false
}) => {
  // Logo fallback state
  const [logoSrc, setLogoSrc] = useState('/Logo/MB_logo.jpg');
  const [logoHasError, setLogoHasError] = useState(false);

  // Use the positioning system
  const {
    position,
    setPosition,
    size,
    setSize,
    visible,
    setVisible,
    registerCollisionHandler
  } = useUIElement(
    'governance-box',
    'governance-box',
    DEFAULT_POSITION,
    DEFAULT_SIZE,
    ZIndexLayer.DIALOGS,
    'manual', // Use manual strategy to prevent automatic repositioning
    10 // High priority
  );

  // Update visibility based on isOpen prop
  useEffect(() => {
    console.log('GovernanceBoxPositioned: isOpen changed to', isOpen);
    setVisible(isOpen);

    // If opening for the first time, ensure it's centered
    if (isOpen && position.x === 0 && position.y === 0) {
      console.log('GovernanceBoxPositioned: Setting initial centered position');
      const centeredX = Math.max(100, window.innerWidth / 2 - size.width / 2);
      const centeredY = Math.max(50, window.innerHeight / 2 - size.height / 2);
      setPosition({ x: centeredX, y: centeredY });
    }
  }, [isOpen, setVisible, position.x, position.y, size.width, size.height]);

  // Update size based on isCollapsed prop
  useEffect(() => {
    if (isCollapsed) {
      setSize(COLLAPSED_SIZE);
    } else {
      setSize(DEFAULT_SIZE);
    }
  }, [isCollapsed, setSize]);

  // Track if user has manually moved the box (used in handleDragStop)
  const [userHasMoved, setUserHasMoved] = useState(false);

  // Register collision handler but don't automatically move
  useEffect(() => {
    registerCollisionHandler((collidingElements) => {
      // Log collision but don't automatically move
      if (collidingElements.includes('context-panel')) {
        console.log('Collision with context panel detected, userHasMoved:', userHasMoved);
      }
    });
  }, [registerCollisionHandler, userHasMoved]);

  // Don't automatically move when context panel opens
  useEffect(() => {
    console.log('Context panel state changed:', isContextPanelOpen);
  }, [isContextPanelOpen]);

  // Listen for collapse_governance_box events
  useEffect(() => {
    const handleCollapseGovernanceBox = (event: CustomEvent) => {
      console.log('GovernanceBoxPositioned: Received collapse_governance_box event');

      // Collapse the governance box if it's not already collapsed
      if (!isCollapsed && onCollapse) {
        console.log('GovernanceBoxPositioned: Collapsing governance box');
        onCollapse();
      }

      // Check if we should reposition to top-right
      if (event.detail?.position === 'top-right') {
        console.log('GovernanceBoxPositioned: Repositioning to top-right');

        // Calculate top-right position (with some margin)
        const topRightX = window.innerWidth - (isCollapsed ? COLLAPSED_SIZE.width : size.width) - 20;
        const topRightY = 20; // 20px from the top

        // Set the new position
        setPosition({ x: topRightX, y: topRightY });

        // Mark as user moved to prevent automatic repositioning
        setUserHasMoved(true);
      }
    };

    // Add event listener
    document.addEventListener('mindback:collapse_governance_box', handleCollapseGovernanceBox as EventListener);

    // Clean up
    return () => {
      document.removeEventListener('mindback:collapse_governance_box', handleCollapseGovernanceBox as EventListener);
    };
  }, [isCollapsed, onCollapse, size.width]);

  // Handle drag start
  const handleDragStart = () => {
    console.log('Drag started');
    // Add a class to the body to indicate dragging is in progress
    document.body.classList.add('governance-dialog-dragging');
  };

  // Handle drag stop
  const handleDragStop = (_e: any, d: any) => {
    console.log('Drag stopped at position:', d.x, d.y);
    setPosition({ x: d.x, y: d.y });
    // Mark that user has manually positioned the box
    setUserHasMoved(true);
    // Remove the dragging class
    document.body.classList.remove('governance-dialog-dragging');
  };

  // Handle resize stop
  const handleResizeStop = (_e: any, _direction: any, ref: any, _delta: any, position: any) => {
    console.log('Resize stopped with size:', ref.style.width, ref.style.height);
    setSize({
      width: parseInt(ref.style.width),
      height: parseInt(ref.style.height)
    });
    setPosition(position);
  };

  // Handle reset position - center in the viewport
  const handleResetPosition = () => {
    console.log('Centering governance box in viewport');
    // Center the box in the viewport
    const centeredX = Math.max(100, window.innerWidth / 2 - size.width / 2);
    const centeredY = Math.max(50, window.innerHeight / 2 - size.height / 2);
    setPosition({ x: centeredX, y: centeredY });
    // Reset the user moved flag
    setUserHasMoved(false);
  };

  // Add a handler for actions coming from the chat
  const handleChatAction = (action: any) => {
    console.log('GovernanceBoxPositioned: Received action from chat:', action);

    // Handle show_chatfork actions by immediately creating the mindsheet
    if (action.type === 'show_chatfork') {
      console.log('GovernanceBoxPositioned: Processing show_chatfork action - IMMEDIATE CREATION');

      // Store the backend data for use in chatfork creation
      const actionData = action.data;
      const chatForkData = {
        text: actionData.title || actionData.text || 'Exploratory Content',
        description: actionData.description || 'Exploratory content',
        intent: 'exploratory',
        full_text: actionData.full_text || actionData.description || 'No detailed content available.',
        root_topic: actionData.root_topic || actionData.title || actionData.text || 'Topic',
        // Include the original content structure
        ...actionData.content,
        // Include template output if available
        templateOutput: actionData.templateOutput || actionData.content
      };

      console.log('GovernanceBoxPositioned: Creating ChatFork mindsheet immediately with data:', chatForkData);

      // Immediately create the ChatFork mindsheet (no second click required)
      try {
        // Register the events
        RegistrationManager.registerEvent(EventType.INTENTION_SELECTED, { name: 'ChatFork' });
        RegistrationManager.registerEvent(EventType.CHATFORK_SELECTED, { timestamp: new Date().toISOString() });

        // Get the MindBookStore
        const mindBookStore = useMindBookStore.getState();

        // Generate a unique name for the new sheet using timestamp
        const timestamp = Date.now();
        const sheetTitle = `chatfork_${timestamp}`;

        // For display purposes, we'll use a sequential number based on the number of existing sheets
        // Get the current sheets and count how many chatforks we already have
        const existingSheets = mindBookStore.sheets;
        const chatforkCount = existingSheets.filter(sheet =>
          sheet.contentType === MindSheetContentType.CHATFORK
        ).length;
        const displayNumber = chatforkCount + 1; // Start from 1
        const displayTitle = `ChatFork ${displayNumber}`;
        console.log(`Creating new chatfork sheet: ${sheetTitle} (display: ${displayTitle})`);

        console.log('GovernanceBoxPositioned: Creating ChatFork with data:', chatForkData);

        // Create the sheet using the ChatFork data
        const sheetId = mindBookStore.createChatForkSheet(sheetTitle, chatForkData);
        console.log('Created ChatFork mindsheet with ID:', sheetId);

        // Register the mindsheet creation event
        RegistrationManager.registerEvent(EventType.MINDSHEET_CREATED, {
          id: sheetId,
          type: 'chatfork',
          title: sheetTitle
        });

        // Set this sheet as active
        mindBookStore.setActiveSheet(sheetId);

        // Show the chatfork in the ChatForkStore
        const chatForkStore = useChatForkStore.getState();
        if (chatForkStore && typeof chatForkStore.showChatFork === 'function') {
          console.log('GovernanceBoxPositioned: Showing ChatFork in ChatForkStore');
          chatForkStore.showChatFork(chatForkData, sheetId);
        } else {
          console.error('GovernanceBoxPositioned: ChatForkStore or showChatFork function not available');
        }

        // Add a message from the assistant
        const assistantMessage = {
          id: (Date.now() + 100).toString(),
          text: `Exploratory mindsheet created: ${chatForkData.text || 'Topic'}`,
          sender: 'assistant' as 'assistant',
          timestamp: new Date()
        };

        // Add the message to the chat
        useChatStore.getState().addMessage(assistantMessage);

        // Dispatch a global event that any component can listen for
        console.log('GovernanceBoxPositioned: Dispatching action to show chatfork');
        const event = new CustomEvent('mindback:show_chatfork', {
          detail: {
            type: 'create_chatfork',
            data: chatForkData,
            sheetId: sheetId
          }
        });

        document.dispatchEvent(event);
      } catch (error) {
        console.error('GovernanceBoxPositioned: Error creating chatfork sheet:', error);
      }

      return; // Action handled - NO NEED TO CALL handleContextChange
    }

    // For other actions, pass to the parent onAction handler if available
    if (onAction) {
      console.log('GovernanceBoxPositioned: Forwarding action to parent:', action);
      onAction(action);
    }
  };

  // Always render but with conditional visibility via CSS
  return (
    <Rnd
      position={position}
      size={isCollapsed ? COLLAPSED_SIZE : size}
      onDragStart={handleDragStart}
      onDragStop={handleDragStop}
      onResizeStop={handleResizeStop}
      minWidth={600}
      minHeight={isCollapsed ? COLLAPSED_SIZE.height : 400}
      bounds="window"
      dragHandleClassName="dialog-header"
      disableDragging={false}
      cancel=".dialog-header-buttons"
      enableResizing={{
        bottom: !isCollapsed,
        bottomRight: !isCollapsed,
        bottomLeft: false,
        right: !isCollapsed,
        left: false,
        top: false,
        topRight: false,
        topLeft: false
      }}
      style={{
        display: visible ? 'block' : 'none',
        zIndex: 'var(--z-index-governance-box)'
      }}
      className={`governance-chat-dialog-container ${isCollapsed ? 'collapsed' : ''}`}
    >
      <div className="governance-dialog">
        <div className="dialog-header">
          <div className="dialog-title">
            <img
              src={logoSrc}
              alt="MindBack Logo"
              className="dialog-header-logo"
              width="24"
              height="24"
              draggable="false"
              onError={(error) => {
                console.error('GovernanceBoxPositioned: Logo failed to load:', error);
                console.log('GovernanceBoxPositioned: Attempted path:', logoSrc);
                
                if (!logoHasError) {
                  // Try with cache busting
                  const cacheBuster = `?v=${Date.now()}`;
                  console.log('GovernanceBoxPositioned: Retrying with cache buster:', `/Logo/MB_logo.jpg${cacheBuster}`);
                  setLogoSrc(`/Logo/MB_logo.jpg${cacheBuster}`);
                  setLogoHasError(true);
                }
              }}
              onLoad={() => {
                console.log('GovernanceBoxPositioned: Logo loaded successfully');
                setLogoHasError(false);
              }}
            />
            <span className="dialog-header-text">Governance Agent</span>
          </div>
          <div className="dialog-header-buttons">
            {onCollapse && (
              <IconButton
                onClick={onCollapse}
                size="small"
                className="dialog-header-button minimize-button"
                title={isCollapsed ? "Expand" : "Minimize"}
              >
                <MinimizeIcon />
              </IconButton>
            )}
            <IconButton
              onClick={handleResetPosition}
              size="small"
              className="dialog-header-button"
              title="Reset Position"
            >
              <RefreshIcon />
            </IconButton>
            <IconButton
              onClick={onClose}
              size="small"
              className="dialog-header-button"
              title="Close"
            >
              <CloseIcon />
            </IconButton>
          </div>
        </div>

        {!isCollapsed && (
          <div className="dialog-content">
            <GovernanceContent onAction={handleChatAction} />
          </div>
        )}
      </div>
    </Rnd>
  );
};

// Internal component for the content of the governance box
const GovernanceContent: React.FC<{
  onAction?: (action: any) => void;
}> = ({
  onAction
}) => {
  // Chat functionality hooks
  const [isLiveLLMEnabled, setIsLiveLLMEnabled] = useState(true);
  const [selectedModel, setSelectedModel] = useState('gpt-4o-mini');
  const [selectedContext, setSelectedContext] = useState('agentic');
  const [showLogging, setShowLogging] = useState(true);

  // Use the chat hook directly - the useChat hook maintains its own state
  const {
    messages,
    isSubmitting,
    handleMessageSubmit,
    handleAction: chatHandleAction,
    handleBuildMindmap
  } = useChat({
    selectedModel,
    useLiveLLM: isLiveLLMEnabled
  }, onAction); // Use onAction prop instead of handleChatAction

  // State management
  const [inputText, setInputText] = useState('');

  // Handle toggle live LLM
  const handleLiveLLMToggle = () => {
    setIsLiveLLMEnabled(!isLiveLLMEnabled);
  };

  // Handle toggle logging
  const handleLoggingToggle = () => {
    setShowLogging(!showLogging);
  };

  // Handle model change
  const handleModelChange = (model: string) => {
    console.log('Model changed to:', model);
    setSelectedModel(model);
  };

  // Handle context change
  const handleContextChange = (context: string) => {
    console.log('Context changed to:', context);
    setSelectedContext(context);

    // Handle different intention types
    if (context === 'teleological') {
      console.log('Teleological intent selected, creating mindsheet and mindmap');

      // Register the intention selection event
      RegistrationManager.registerEvent(EventType.INTENTION_SELECTED, { name: 'Teleological' });

      // Get the MindBookStore
      const mindBookStore = useMindBookStore.getState();

      // Generate a unique name for the new sheet using timestamp
      const timestamp = Date.now();
      const sheetTitle = `teleological_${timestamp}`;

      // For display purposes, we'll use a sequential number based on the number of existing sheets
      // Get the current sheets and count how many mindmaps we already have
      const existingSheets = mindBookStore.sheets;
      const mindmapCount = existingSheets.filter(sheet =>
        sheet.contentType === MindSheetContentType.MINDMAP
      ).length;
      const displayNumber = mindmapCount + 1; // Start from 1
      const displayTitle = `Teleological Mindmap ${displayNumber}`;
      console.log(`Creating new teleological mindmap sheet: ${sheetTitle} (display: ${displayTitle})`);

      // Create a simple MBCP structure with just a root node
      const rootNodeId = uuidv4();
      const mbcpData = {
        intent: 'teleological',
        text: displayTitle,
        description: `A mindmap for teleological intent #${displayNumber}`,
        created: new Date().toISOString(),
        mindmap: {
          root: {
            id: rootNodeId,
            text: displayTitle,
            description: `Click to edit this mindmap #${displayNumber}`,
            metadata: {
              intent: 'teleological',
              tags: ['mindmap'],
              nodePath: '1.0',
              isEditing: false,
              positionsStabilized: true,
              uniqueIdentifier: uuidv4() // Add a unique identifier to ensure uniqueness
            },
            children: []
          }
        },
        // Add a unique identifier at the top level to ensure each mindmap is unique
        uniqueIdentifier: uuidv4()
      };

      try {
        // 1. Create the mindsheet
        console.log('GovernanceBoxPositioned: Creating mindsheet in MindBookStore');
        const sheetId = mindBookStore.createMindMapSheet(sheetTitle, mbcpData);

        if (!sheetId) {
          console.error('GovernanceBoxPositioned: Failed to create mindsheet');
          return;
        }

        console.log('GovernanceBoxPositioned: Successfully created mindsheet with ID:', sheetId);

        // 2. Register the mindsheet creation event
        RegistrationManager.registerEvent(EventType.MINDSHEET_CREATED, {
          id: sheetId,
          type: 'mindmap',
          title: sheetTitle
        });

        // 3. Set it as the active sheet
        console.log('GovernanceBoxPositioned: Setting as active sheet');
        mindBookStore.setActiveSheet(sheetId);

        // 4. Initialize the mindmap using the MindSheetService
        // This will use the sheet-specific store system and handle all initialization
        console.log('GovernanceBoxPositioned: Initializing teleological mindmap using MindSheetService for sheet:', sheetId);
        const success = initializeMindMapSheet(sheetId, mbcpData);

        if (success) {
          console.log('GovernanceBoxPositioned: Successfully initialized teleological mindmap for sheet:', sheetId);

          // Dispatch an event to notify components that the mindmap is ready
          const event = new CustomEvent('mindback:mindmap_initialized', {
            detail: {
              sheetId: sheetId,
              hasData: true,
              shouldCenterView: true
            }
          });
          document.dispatchEvent(event);
          console.log('GovernanceBoxPositioned: Dispatched mindmap_initialized event for teleological sheet:', sheetId);
        } else {
          console.error('GovernanceBoxPositioned: Failed to initialize teleological mindmap for sheet:', sheetId);
        }

        // 5. Add a system message indicating the mindmap was created
        const systemMessage = {
          id: Date.now().toString(),
          text: 'Teleological mindmap created successfully',
          sender: 'system' as 'system',
          timestamp: new Date()
        };
        useChatStore.getState().addMessage(systemMessage);

      } catch (error) {
        console.error('GovernanceBoxPositioned: Error creating mindsheet:', error);
      }
    } else if (context === 'mindmap') {
      console.log('Mindmap selected, creating new mindmap');

      // Register the intention selection event
      RegistrationManager.registerEvent(EventType.INTENTION_SELECTED, { name: 'Mindmap' });

      // Get the MindBookStore
      const mindBookStore = useMindBookStore.getState();

      // Generate a unique name for the new sheet using timestamp
      const timestamp = Date.now();
      const sheetTitle = `mindmap_${timestamp}`;

      // For display purposes, we'll use a sequential number based on the number of existing sheets
      // Get the current sheets and count how many mindmaps we already have
      const existingSheets = mindBookStore.sheets;
      const mindmapCount = existingSheets.filter(sheet =>
        sheet.contentType === MindSheetContentType.MINDMAP
      ).length;
      const displayNumber = mindmapCount + 1; // Start from 1
      const displayTitle = `Mindmap ${displayNumber}`;
      console.log(`Creating new mindmap sheet: ${sheetTitle} (display: ${displayTitle})`);

      // Create a minimal MBCP structure for the mindsheet
      const mbcpData = {
        mindmap: {
          root: {
            id: uuidv4(),
            text: displayTitle,
            description: `Click to edit this mindmap #${displayNumber}`,
            metadata: {
              intent: 'teleological',
              tags: ['mindmap']
            },
            children: [] // No children initially
          }
        },
        text: displayTitle,
        description: `Click to edit this mindmap #${displayNumber}`,
        intent: 'teleological'
      };

      // Create a new mindsheet in the MindBookStore
      const sheetId = mindBookStore.createMindMapSheet(sheetTitle, mbcpData);
      console.log('Created mindsheet with ID:', sheetId);

      // Register the mindsheet creation event
      RegistrationManager.registerEvent(EventType.MINDSHEET_CREATED, {
        id: sheetId,
        type: 'mindmap',
        title: sheetTitle
      });

      // Set this sheet as active
      mindBookStore.setActiveSheet(sheetId);

      // Initialize the mindmap using the MindSheetService
      // This will use the sheet-specific store system and handle all initialization
      console.log('GovernanceBoxPositioned: Initializing mindmap using MindSheetService for sheet:', sheetId);
      const success = initializeMindMapSheet(sheetId, mbcpData);

      if (success) {
        console.log('GovernanceBoxPositioned: Successfully initialized mindmap for sheet:', sheetId);

        // Dispatch an event to notify components that the mindmap is ready
        const event = new CustomEvent('mindback:mindmap_initialized', {
          detail: {
            sheetId: sheetId,
            hasData: true,
            shouldCenterView: true
          }
        });
        document.dispatchEvent(event);
        console.log('GovernanceBoxPositioned: Dispatched mindmap_initialized event for sheet:', sheetId);
      } else {
        console.error('GovernanceBoxPositioned: Failed to initialize mindmap for sheet:', sheetId);
      }

      // Add a message from the assistant prompting the user to enter the topic
      const assistantMessage = {
        id: (Date.now() + 100).toString(),
        text: 'A new mindmap has been created. You can now edit it.',
        sender: 'assistant' as 'assistant', // Type assertion to fix type issue
        timestamp: new Date()
      };

      // Add the message to the chat
      useChatStore.getState().addMessage(assistantMessage);

      // No need to call onAction - the MindSheet component will handle rendering
      // based on the active sheet in MindBookStore
    } else if (context === 'chatfork') {
      console.log('ChatFork selected, creating ChatFork mindsheet');
      // Register the chatfork selection event
      RegistrationManager.registerEvent(EventType.INTENTION_SELECTED, { name: 'ChatFork' });
      RegistrationManager.registerEvent(EventType.CHATFORK_SELECTED, { timestamp: new Date().toISOString() });

      try {
        // Get the MindBookStore
        const mindBookStore = useMindBookStore.getState();

        // Generate a unique name for the new sheet using timestamp
        const timestamp = Date.now();
        const sheetTitle = `chatfork_${timestamp}`;

        // For display purposes, we'll use a sequential number based on the number of existing sheets
        // Get the current sheets and count how many chatforks we already have
        const existingSheets = mindBookStore.sheets;
        const chatforkCount = existingSheets.filter(sheet =>
          sheet.contentType === MindSheetContentType.CHATFORK
        ).length;
        const displayNumber = chatforkCount + 1; // Start from 1
        const displayTitle = `ChatFork ${displayNumber}`;
        console.log(`Creating new chatfork sheet: ${sheetTitle} (display: ${displayTitle})`);

        // Determine the data source for ChatFork creation
        let chatForkData;
        // Create default ChatFork data for manual context selection
        chatForkData = {
          text: displayTitle,
          description: 'Exploratory content',
          intent: 'exploratory',
          full_text: 'No detailed content available.',
          root_topic: 'Topic',
          content: {},
          templateOutput: {}
        };

        console.log('GovernanceBoxPositioned: Creating ChatFork with default data:', chatForkData);

        // Create the sheet using the ChatFork data
        const sheetId = mindBookStore.createChatForkSheet(sheetTitle, chatForkData);
        console.log('Created ChatFork mindsheet with ID:', sheetId);

        // Register the mindsheet creation event
        RegistrationManager.registerEvent(EventType.MINDSHEET_CREATED, {
          id: sheetId,
          type: 'chatfork',
          title: sheetTitle
        });

        // Set this sheet as active
        mindBookStore.setActiveSheet(sheetId);

        // Show the chatfork in the ChatForkStore
        const chatForkStore = useChatForkStore.getState();
        if (chatForkStore && typeof chatForkStore.showChatFork === 'function') {
          console.log('GovernanceBoxPositioned: Showing ChatFork in ChatForkStore');
          chatForkStore.showChatFork(chatForkData, sheetId);
        } else {
          console.error('GovernanceBoxPositioned: ChatForkStore or showChatFork function not available');
        }

        // Add a message from the assistant
        const assistantMessage = {
          id: (Date.now() + 100).toString(),
          text: 'A new chatfork has been created. You can now explore the topic.',
          sender: 'assistant' as 'assistant',
          timestamp: new Date()
        };

        // Add the message to the chat
        useChatStore.getState().addMessage(assistantMessage);

        // Dispatch a global event that any component can listen for
        console.log('GovernanceBoxPositioned: Dispatching action to show chatfork');
        const event = new CustomEvent('mindback:show_chatfork', {
          detail: {
            type: 'create_chatfork',
            data: chatForkData,
            sheetId: sheetId
          }
        });

        document.dispatchEvent(event);
      } catch (error) {
        console.error('GovernanceBoxPositioned: Error creating chatfork sheet:', error);
      }
    }
  };

  // Handle input change
  const handleInputChange = (text: string) => {
    setInputText(text);
  };

  // Handle message submit
  const handleSubmit = () => {
    if (inputText.trim() && !isSubmitting) {
      handleMessageSubmit(inputText);
      setInputText('');
    }
  };

  return (
    <div className="governance-chat-content">
      <div className="message-container">
        <MessageList
          messages={messages}
          onAction={chatHandleAction}
          onBuildMindmap={handleBuildMindmap}
          showBuildMindmapButton={
            // Only show the button if we're in mindmap context OR
            // if there's a teleological message that doesn't already have a mindmap
            selectedContext === 'mindmap' ||
            (messages.some(msg =>
              msg.sender === 'assistant' &&
              msg.responseType?.type === 'teleological' &&
              !msg.mindmapCreated // Don't show if a mindmap was already created
            ))
          }
        />
      </div>

      <div className="model-selector-container">
        <ModelSelector
          selectedModel={selectedModel}
          onModelChange={handleModelChange}
          selectedContext={selectedContext}
          onContextChange={handleContextChange}
          useLiveLLM={isLiveLLMEnabled}
          onLiveLLMToggle={handleLiveLLMToggle}
          showLogging={showLogging}
          onLoggingToggle={handleLoggingToggle}
        />
      </div>

      <div className="input-container">
        <MessageInput
          value={inputText}
          onChange={handleInputChange}
          onSubmit={handleSubmit}
          // Remove props that don't exist on MessageInputProps
          // isSubmitting={isSubmitting}
          placeholder="Type your message..."
        />
      </div>
    </div>
  );
};

export default GovernanceBoxPositioned;

