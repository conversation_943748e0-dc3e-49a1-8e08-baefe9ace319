# MindBack Frontend

## Architecture Overview

This application uses a modular architecture with React and TypeScript. The main components are:

1. **MindSheet Framework** - The primary architecture for all content including mindmaps
2. **MindMapCanvas** - The interactive canvas that renders mindmaps within MindSheets
3. **MindBookStore** - Central state management for the MindBook/MindSheet architecture

## Important Notes

### Component Structure

- The application uses a modular approach with components, contexts, and hooks separated into their own files
- All mindmap operations now use the MindSheet framework
- Legacy OptimizedMindMap_Modular has been removed to prevent rendering conflicts

### TypeScript + react-konva

The application uses `react-konva` for canvas rendering, which has some TypeScript compatibility issues. We've addressed this by:

1. Creating type assertions for Konva components in `MindMapCanvasSimple.tsx`
2. Using aliases like `KonvaStage`, `KonvaLayer`, etc. with explicit typing
3. Keeping the original implementation in `MindMapCanvas.legacy.tsx` for reference only
4. Including both `onDragMove` and `onDragEnd` handlers on all draggable components (Stage, Group, etc.) to ensure state is always updated

### Context vs Store

- The application uses both `MindMapContext` (React Context) and `MindMapStore` (Zustand)
- All new components should use the context through the `useMindMap` hook
- Context must be provided through `MindMapProvider` at the top level

### Known Warnings

- **React Router Warnings**: The console shows warnings about future changes in React Router v7. These are informational only and don't affect functionality. When upgrading to React Router v7, we'll need to enable the `v7_startTransition` and `v7_relativeSplatPath` future flags.
- **Warning Suppression**: Development-only warnings are suppressed in `index.tsx` using a console.warn override. This keeps the console clean while maintaining all functional error messages.

## Development Guidelines

1. Always use components through their exported names in index files
2. Ensure components that use `useMindMap` are wrapped in a `MindMapProvider`
3. When adding new Konva components, follow the pattern in `MindMapCanvasSimple.tsx`
4. For draggable elements, always include both `onDragMove` and `onDragEnd` handlers
5. Remember that KonvaStage components need drag handlers too, not just nodes
6. Update documentation in `0StageOfDev.md` when making significant changes

## Getting Started

```bash
cd frontend
npm install
npm run dev
```

The application will be available at http://localhost:5173/ 