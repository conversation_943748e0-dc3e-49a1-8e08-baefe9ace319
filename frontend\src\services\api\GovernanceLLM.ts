// This file will only handle direct chat interactions
/*
import { getApiBaseUrl } from '../../components/MindMap/config/api';
import { useMindMapStore } from '../../components/MindMap/core/state/MindMapStore';
import { createNode } from '../../components/MindMap/core/models/Node';
import { createTreeConnection } from '../../components/MindMap/core/models/Connection';
*/

// Import required modules for sheet-specific store management
import { useMindBookStore } from '../../core/state/MindBookStore';

export enum ResponseTypeEnum {
  Factual = 'factual',
  Exploratory = 'exploratory',
  Teleological = 'teleological',
  Instantiation = 'instantiation',
  Misc = 'misc'
}

export interface ResponseAnalysis {
  topic?: string;
  keyPoints?: string[];
  suggestedStructure?: string;
}

export interface ResponseType {
  type: ResponseTypeEnum;
  requiresMindmap: boolean;
  requiresChatFork: boolean;
  requiresTemplate?: boolean; // Made optional as it might not always be present
  analysis?: {
    topic: string;
    keyPoints: string[];
    suggestedStructure: string;
  };
}

export enum ActionTypeEnum {
  CreateMindmap = 'create_mindmap',
  ShowChatFork = 'show_chatfork',
  ProvideContext = 'provide_context',
  ExploreTopic = 'explore_topic'
}

export interface SuggestedAction {
  type: ActionTypeEnum;
  label: string;
  data?: any;
}

export interface ChatResponse {
  text: string;
  responseType?: ResponseType;  // Make responseType optional
  suggestedActions: SuggestedAction[];
  templateOutput?: any;
  // Add fields for ChatFork content
  full_text?: string;  // The detailed content for ChatFork
  root_topic?: string; // The topic/title for ChatFork
}

/*
export interface ProjectResponse {
  success: boolean;
  error?: string;
}

// Define types for rich metadata
export interface ActionItem {
  title?: string;
  owner?: string;
  due_date?: string;
  system?: string;
  status?: 'pending' | 'in_progress' | 'done';
}

export interface NodeMetadata {
  intent: 'factual' | 'exploratory' | 'teleological' | 'instantiation' | 'miscellaneous';
  agent: 'blue_hat' | 'white_hat' | 'red_hat' | 'black_hat' | 'yellow_hat' | 'green_hat' | null;
  tags: string[];
  action: ActionItem | null;
}

export interface MBCPNode {
  id?: string;
  text: string;
  description: string;
  intent: NodeMetadata['intent'];
  metadata: NodeMetadata;
  children?: MBCPNode[];
}

export interface LLMResponse {
  success: boolean;
  content: MBCPNode;
  model?: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  metadata?: {
    prompt_type?: string;
    intent?: string;
    template_type?: string;
  };
  validation_info?: {
    validated: boolean;
    validation_errors?: string;
    metadata_present?: {
      intent: boolean;
      agent: boolean;
      tags: boolean;
      action: boolean;
    };
  };
  error?: string;
}
*/

export class ChatService {
  private static instance: ChatService;
  // private currentModel: string = 'gpt-3.5-turbo';
  // private useMockResponses: boolean = false;
  // private apiBaseUrl: string;
  // private api: any; // Replace with actual API client type

  private constructor() {
    // this.apiBaseUrl = getApiBaseUrl();
    /*
    this.api = {
      post: async (url: string, data: any) => {
        // Don't add hardcoded port to URLs - rely on proxying instead
        const fullUrl = url;

        console.log(`Making API request to: ${fullUrl}`);
        console.log('Request data:', JSON.stringify(data));

        const response = await fetch(fullUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`API error ${response.status}: ${errorText}`);
          throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
        }

        const responseData = await response.json();
        console.log('API response:', responseData);
        return {
          data: responseData
        };
      }
    };
    */
  }

  static getInstance(): ChatService {
    if (!ChatService.instance) {
      ChatService.instance = new ChatService();
    }
    return ChatService.instance;
  }
  /*
  setUseMockResponses(useMock: boolean): void {
    this.useMockResponses = useMock;
  }

  updateModel(model: string): void {
    this.currentModel = model;
  }

  async sendMessage(message: string, useCrewAI: boolean = false): Promise<ChatResponse> {
    console.log(`Sending message: "${message}"`);

    // CrewAI has been removed, always use normal interaction
    return this.handleNormalInteraction(message);
  }

  public async handleCrewAIInteraction(userMessage: string): Promise<ChatResponse> {
    try {
      console.log('Using CrewAI for processing:', userMessage);

      const response = await fetch(`/api/crew/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: userMessage
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('CrewAI error response:', errorText);
        throw new Error(`CrewAI API error: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Raw CrewAI response:', data);

      if (!data || typeof data !== 'object') {
          throw new Error('Invalid response structure from CrewAI');
      }

      // Handle direct factual answers
      if (data.mode === 'direct_answer' && data.tool_output?.content) {
          return {
              text: data.tool_output.content,
              responseType: { type: ResponseTypeEnum.Factual, requiresMindmap: false, requiresChatFork: false, requiresTemplate: false },
              suggestedActions: []
          };
      }
      // Handle template under construction responses
      else if (data.mode === 'template_response' && data.template_selection?.intent_classification && data.tool_output?.content) {
          const intentType = data.template_selection.intent_classification.toLowerCase();
          const validIntents = [ResponseTypeEnum.Factual, ResponseTypeEnum.Exploratory, ResponseTypeEnum.Teleological, ResponseTypeEnum.Instantiation];

          if (!validIntents.includes(intentType as ResponseTypeEnum)) {
              console.error(`Unrecognized intent type from backend: ${intentType}`);
              // Fallback to a default or throw error
              return {
                  text: "Received an unexpected response structure.",
                  responseType: { type: ResponseTypeEnum.Misc, requiresMindmap: false, requiresChatFork: false, requiresTemplate: false },
                  suggestedActions: []
              };
          }

          return {
              text: data.tool_output.content,
              responseType: {
                  type: intentType as ResponseTypeEnum,
                  requiresMindmap: intentType === ResponseTypeEnum.Teleological || intentType === ResponseTypeEnum.Instantiation,
                  requiresChatFork: intentType === ResponseTypeEnum.Exploratory,
                  requiresTemplate: true // Assuming template response implies template needed
              },
              suggestedActions: [
                  { type: ActionTypeEnum.ProvideContext, label: 'Simple Answer', data: { option: 1 } },
                  { type: ActionTypeEnum.ExploreTopic, label: 'Explore Topic', data: { topic: data.template_selection.topic || userMessage } },
                  { type: ActionTypeEnum.CreateMindmap, label: 'Create Mindmap', data: { structure: data.template_selection.structure_suggestion || null } }
              ]
          };
  }
      // Handle cases where the response structure is not recognized
      else {
          console.warn('CrewAI response structure not fully recognized or incomplete:', data);
          return {
              text: data.tool_output?.content || "Received an incomplete or unrecognized response.",
              responseType: { type: ResponseTypeEnum.Misc, requiresMindmap: false, requiresChatFork: false, requiresTemplate: false },
              suggestedActions: []
          };
      }

    } catch (error) {
      console.error('Error handling CrewAI interaction:', error);
      return {
        text: 'Sorry, there was an error processing your request with CrewAI.',
        responseType: { type: ResponseTypeEnum.Misc, requiresMindmap: false, requiresChatFork: false, requiresTemplate: false },
        suggestedActions: []
      };
    }
  }

  private async handleNormalInteraction(userMessage: string): Promise<ChatResponse> {
    console.log('Handling normal LLM interaction...');
    // No mock responses - always use real API

    try {
      const isJsonFormatSupported = this.supportsJsonResponseFormat(this.currentModel);
      console.log(`Model ${this.currentModel} supports JSON format: ${isJsonFormatSupported}`);

      const requestBody = {
        model: this.currentModel,
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: userMessage }
        ],
        // Only add response_format if the model supports it
        ...(isJsonFormatSupported && { response_format: { type: 'json_object' } })
      };

      console.log('Sending request to /api/llm/chat with body:', JSON.stringify(requestBody, null, 2));

      const response = await this.api.post(`/api/llm/chat`, requestBody);

      console.log('Raw API Response:', JSON.stringify(response.data, null, 2));

      if (!response.data || !response.data.choices || response.data.choices.length === 0) {
        throw new Error('Invalid response structure from LLM API');
      }

      let contentText = response.data.choices[0].message?.content;
      let responseData: any = {};

      if (isJsonFormatSupported && contentText) {
        try {
          // Attempt to parse the content as JSON
          responseData = JSON.parse(contentText);
          console.log('Parsed JSON content:', responseData);
          // If successful, extract the main text if available (assuming a structure like { answer: "..." })
          contentText = responseData.answer || responseData.text || JSON.stringify(responseData);
        } catch (e) {
          console.warn('Content was not valid JSON, treating as plain text:', contentText);
          // Keep contentText as is, responseData remains empty
          responseData = {};
        }
      } else if (contentText) {
          // If not JSON format or no content, ensure contentText is a string
          console.log('Received plain text content:', contentText);
          responseData = {}; // No structured data expected
      } else {
          throw new Error('No content received from LLM API');
        }

      // Basic response structure
      let chatResponse: ChatResponse = {
        text: contentText || "Sorry, I couldn't generate a response.",
        suggestedActions: [],
        responseType: {
            type: ResponseTypeEnum.Factual, // Default to factual
            requiresMindmap: false,
            requiresChatFork: false,
            requiresTemplate: false
        }
      };

      // No hardcoded intent detection - rely on the LLM's intent classification
      // The backend will provide the intent in the response
      console.log('Using intent from LLM response instead of hardcoded detection');

      console.log('Processed Chat Response:', chatResponse);
      return chatResponse;

    } catch (error) {
      console.error('❌ CRITICAL: Error communicating with LLM API:', error);
      // No fallback - let the error be visible to the user
      throw new Error(`LLM API communication failed: ${error.message || error}`);
    }
  }

  async getHealth(): Promise<any> {
    console.log('Checking LLM health...');
    try {
      const response = await this.api.post(`/api/llm/health`, {});
      console.log('Health check response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error checking LLM health:', error);
      return { status: 'error', message: error.message };
    }
  }

  // --- MindMap Creation Logic ---
  async createMindmapFromTopic(topic: string): Promise<ProjectResponse> {
    console.log(`Requesting mind map generation for topic: "${topic}"`);

    // Get the current state
    const store = useMindMapStore.getState();
    const currentNodes = store.nodes ? Object.values(store.nodes).map(n => ({ id: n.id, text: n.text })) : [];
    const currentConnections = store.connections || [];

    try {
      const isJsonFormatSupported = this.supportsJsonResponseFormat(this.currentModel);

      const requestBody = {
          model: this.currentModel,
          messages: [
              { role: 'system', content: 'You are an AI assistant specialized in creating structured mind maps in JSON format based on the MBCP (MindBack Content Protocol). Generate a valid MBCP JSON structure for the given topic. The structure must have a single root node with text, description, intent, metadata (including intent, agent, tags, action), and potentially children nodes following the same recursive structure. Focus on providing a logical hierarchy.' },
              { role: 'user', content: `Create a mind map structure for the topic: "${topic}". Current map context (if any): Nodes: ${JSON.stringify(currentNodes)}, Connections: ${JSON.stringify(currentConnections)}` }
          ],
          // Ensure JSON output format if supported
          ...(isJsonFormatSupported && { response_format: { type: 'json_object' } })
      };

      console.log('Sending request to /api/llm/chat for mindmap generation:', JSON.stringify(requestBody, null, 2));

      const response = await this.api.post(`/api/llm/chat`, requestBody);
      console.log('Raw API Response for mindmap:', JSON.stringify(response.data, null, 2));

      if (!response.data || !response.data.choices || response.data.choices.length === 0) {
        throw new Error('Invalid response structure from LLM API during mindmap generation');
      }

      let contentText = response.data.choices[0].message?.content;
      let mindmapData: any = null;

      if (contentText) {
          try {
              // Attempt to parse the content as JSON, assuming it's the MBCP structure
              mindmapData = JSON.parse(contentText);
              console.log('Parsed MindMap JSON content:', mindmapData);

              // Basic validation (can be expanded)
              if (!mindmapData || typeof mindmapData !== 'object' || !mindmapData.text) {
                  console.error("Parsed JSON is not a valid MBCP structure (missing root text).");
                  throw new Error('Parsed JSON is not a valid MBCP root node.');
              }

          } catch (e) {
              console.error('Content was not valid JSON or failed validation, cannot process mindmap:', contentText, e);
              // If parsing fails, we cannot proceed with creating the mindmap from this response
              return {
                  success: false,
                  error: 'Failed to parse LLM response into a valid mind map structure.'
              };
          }
      } else {
          throw new Error('No content received from LLM API for mindmap generation');
      }

      // If we have valid mindmapData, process it
      if (mindmapData) {
          this.processMindmapData(mindmapData); // Pass the root node object
        return { success: true };
      } else {
          // This case should theoretically be caught by the error handling above
          return { success: false, error: 'Mind map data was not generated or processed correctly.' };
      }

    } catch (error) {
      console.error('Error creating mind map from topic:', error);
      return {
        success: false,
        error: error.message || 'An unknown error occurred during mind map generation.'
      };
    }
  }

  private processChildNode(
    nodeData: any,
    parentId: string,
    index: number,
    nodesDict: Record<string, any>,
    connectionsArray: any[],
    parentPath: string
  ) {
    try {
      if (!nodeData || typeof nodeData !== 'object' || !nodeData.text) {
        console.warn(`Skipping invalid child node data at index ${index} for parent ${parentId}:`, nodeData);
        return; // Skip this invalid child
      }

      const nodePath = `${parentPath}.${index + 1}`;
      console.log(`Processing child node: ${nodePath} - ${nodeData.text}`);

      // We no longer add the index prefix to the node text
      // because we're displaying the path separately in the node
      let nodeText = nodeData.text;

      // Create the child node
      const childNode = createNode(
        nodeText,
        0, // Positioned by layout
        0, // Positioned by layout
        {
          description: nodeData.description || '',
          metadata: {
            nodePath: nodePath,
            // Store any additional metadata from the LLM response
            intent: nodeData.intent,
            agent: nodeData.agent,
            tags: nodeData.tags || []
          }
        }
      );

      // Add child node to nodes dictionary
      nodesDict[childNode.id] = childNode;

      // Create connection between parent and child
      const connection = createTreeConnection({
        parent: parentId,
        child: childNode.id
      });

      // Add connection to connections array
      connectionsArray.push(connection);

      // Process this node's children recursively
      if (nodeData.children && Array.isArray(nodeData.children) && nodeData.children.length > 0) {
        console.log(`Processing ${nodeData.children.length} children for node ${nodePath}`);

        // Process each child of this node
        nodeData.children.forEach((grandchildData: any, childIndex: number) => {
          this.processChildNode(
            grandchildData,
            childNode.id,
            childIndex,
            nodesDict,
            connectionsArray,
            nodePath
          );
        });
      }
    } catch (error) {
      console.error(`Error processing child node:`, error);
    }
  }

  private processMindmapData(rootNodeData: any) {
    try {
      if (!rootNodeData || !rootNodeData.text) {
        console.error('Invalid root node data:', rootNodeData);
        return;
      }

      // CRITICAL FIX: Get the current active sheet ID and use sheet-specific store
      const mindBookStore = useMindBookStore.getState();
      const activeSheetId = mindBookStore.activeSheetId;

      if (!activeSheetId) {
        console.error('GovernanceLLM: No active sheet ID found, cannot process mindmap data');
        return;
      }

      // Import the sheet-specific store service
      const { getSheetMindMapStore } = require('../../core/services/StoreService');
      const sheetStore = getSheetMindMapStore(activeSheetId);
      const store = sheetStore.getState();

      console.log('====== PROCESSING MINDMAP DATA ======');
      console.log('Active Sheet ID:', activeSheetId);
      console.log('Root Node:', rootNodeData);
      console.log('Children nodes:', rootNodeData.children);
      console.log('Current sheet store state:', store);

      // Clear existing nodes and connections in the sheet-specific store
      if (store.clearAll) {
        store.clearAll();
        console.log('Cleared existing mindmap data from sheet store');
      } else {
        // Fallback if clearAll is not available - update the sheet store
        sheetStore.setState({
          nodes: {},
          connections: [],
          selectedNodeId: null,
          selectedConnectionId: null
        });
        console.log('Reset sheet store state (fallback method)');
      }

      // Create nodes and connections dictionary to track all nodes
      const nodesDict: Record<string, any> = {};
      const connectionsArray: any[] = [];

      // Create the root node without hardcoded styling
      const rootNode = createNode(
        rootNodeData.text,
        0, // Will be positioned by layout algorithm
        0, // Will be positioned by layout algorithm
        {
          description: rootNodeData.description || '',
          metadata: {
            nodePath: '1.0', // Standard path for root
            intent: rootNodeData.intent,
            agent: rootNodeData.agent,
            tags: rootNodeData.tags || []
          }
        }
      );

      console.log('Created root node:', rootNode);

      // Add root node to nodes dictionary
      nodesDict[rootNode.id] = rootNode;

      // Process children recursively only if they exist
      if (rootNodeData.children && Array.isArray(rootNodeData.children) && rootNodeData.children.length > 0) {
        console.log(`Processing ${rootNodeData.children.length} children nodes`);

        // Process each child - no hardcoded count or structure
        rootNodeData.children.forEach((childData: any, index: number) => {
          this.processChildNode(childData, rootNode.id, index, nodesDict, connectionsArray, '1');
        });
      } else {
        console.warn('No children found in root node data or empty children array');
      }

      // Update the sheet-specific store with all nodes and connections
      sheetStore.setState({
        nodes: nodesDict,
        connections: connectionsArray,
        rootNodeId: rootNode.id,
        selectedNodeId: rootNode.id
      });

      console.log('Updated sheet store with new nodes and connections:');
      console.log('Sheet ID:', activeSheetId);
      console.log('Nodes:', Object.keys(nodesDict).length);
      console.log('Connections:', connectionsArray.length);

      // Update the layout if available
      if (store.updateLayout) {
        store.updateLayout('topDown');
        console.log('Layout updated to topDown');
      }

      // CRITICAL: Save the sheet state to persistence
      const { saveSheetMindMapStoreState } = require('../../core/services/StoreService');
      saveSheetMindMapStoreState(activeSheetId);
      console.log('Saved sheet state to persistence');

      // After processing, log the final state
      console.log('Final sheet store state:', sheetStore.getState());
      console.log('====== MINDMAP PROCESSING COMPLETE ======');
    } catch (error) {
      console.error('Error processing mindmap data:', error);
    }
  }

  // Mock response method removed - only real API responses allowed

  private updateLayout(): void {
    // Get the current active sheet ID and use sheet-specific store
    const mindBookStore = useMindBookStore.getState();
    const activeSheetId = mindBookStore.activeSheetId;

    if (!activeSheetId) {
      console.warn('GovernanceLLM: No active sheet ID found, cannot update layout');
      return;
    }

    const { getSheetMindMapStore } = require('../../core/services/StoreService');
    const sheetStore = getSheetMindMapStore(activeSheetId);
    const store = sheetStore.getState();

    if (store.updateLayout) {
      // CONSOLIDATED: Use governance-integrated updateLayout method
      store.updateLayout('topDown', 'system').then((success) => {
        if (success) {
          console.log('GovernanceLLM: Layout applied successfully via UnifiedLayoutManager for sheet:', activeSheetId);
        } else {
          console.warn('GovernanceLLM: Layout request was rejected by governance for sheet:', activeSheetId);
        }
      }).catch(error => {
        console.error('GovernanceLLM: Error applying layout for sheet:', activeSheetId, error);
      });
    }
  }

  private supportsJsonResponseFormat(model: string): boolean {
    // Only these specific models are known to support response_format: json
    const supportedModels = [
      'gpt-4-turbo',
      'gpt-4-1106',
      'gpt-4-0125',
      'gpt-3.5-turbo-1106',
      'gpt-3.5-turbo-0125'
    ];

    // Check if the model name contains any of the supported versions
    return supportedModels.some(supported => model.includes(supported));
  }
  */
} // End of ChatService class