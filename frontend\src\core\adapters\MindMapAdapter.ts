/**
 * MindMapAdapter
 *
 * Adapter for connecting MindMap components to the MindBook.
 * Handles the creation and management of mindmaps within sheets.
 * 
 * UPDATED: Now uses the unified MBCP processor for all data format handling.
 */

// Legacy global MindMapStore import removed - using sheet-specific stores via getMindMapStore
import { useMindBookStore } from '../state/MindBookStore';
import { getMindMapStore } from '../state/MindMapStoreFactory';
import { processMBCPData, MBCPData, MBCPNode } from '../mbcp/MBCPProcessor';

/**
 * Initialize a mindmap in the current active sheet using unified MBCP processor.
 *
 * This function validates and normalizes MBCP data, then processes it through the
 * unified processor to create a mindmap in the active sheet.
 *
 * @param mbcpData The MBCP data to use for the mindmap
 * @returns True if the mindmap data was processed successfully, false otherwise
 */
export const initializeMindMap = (mbcpData: any): boolean => {
  try {
    console.log('MindMapAdapter: Initializing mindmap with unified MBCP processor');

    if (!mbcpData) {
      console.error('MindMapAdapter: No MBCP data provided');
      return false;
    }

    // Get the MindBook store
    const mindBookStore = useMindBookStore.getState();

    // Get the active sheet
    const activeSheetId = mindBookStore.activeSheetId;
    if (!activeSheetId) {
      console.error('MindMapAdapter: No active sheet found');
      return false;
    }

    // Get the active sheet
    const activeSheet = mindBookStore.sheets.find(sheet => sheet.id === activeSheetId);
    if (!activeSheet) {
      console.error('MindMapAdapter: Active sheet not found');
      return false;
    }

    // Update the sheet content with the MBCP data
    console.log('MindMapAdapter: Updating sheet content for sheet:', activeSheet.id);
    mindBookStore.updateSheetContent(activeSheet.id, mbcpData);

    // Process MBCP data using unified processor
    const result = processMBCPData(mbcpData);
    
    if (!result.success) {
      console.error('MindMapAdapter: Failed to process MBCP data:', result.error);
      return false;
    }

    // Dispatch an event to notify the MindSheet component that new data is available
    console.log('MindMapAdapter: Dispatching data-updated event for sheet:', activeSheet.id);
    const event = new CustomEvent('mindback:mindmap_data_updated', {
      detail: {
        sheetId: activeSheet.id,
        hasData: true,
        shouldCenterView: true,
        shouldCollapseGovernanceBox: true,
        rootNodeId: result.rootNodeId
      }
    });
    document.dispatchEvent(event);

    console.log('MindMapAdapter: Successfully initialized mindmap with root node ID:', result.rootNodeId);
    return true;

  } catch (error) {
    console.error('MindMapAdapter: Error initializing mindmap:', error);
    return false;
  }
};

/**
 * Process children nodes using unified MBCP processor.
 * 
 * This function is now a wrapper around the unified processor's child handling.
 * It maintains the same interface for backward compatibility.
 *
 * @param storeState The state of the MindMap store
 * @param parentId The ID of the parent node
 * @param children The children nodes to process
 */
export const processChildNodes = (
  storeState: any,
  parentId: string,
  children: MBCPNode[]
): void => {
  try {
    console.log('MindMapAdapter: Processing child nodes via unified processor');
    console.log('MindMapAdapter: Children count:', children.length);

    if (!children || children.length === 0) {
      console.log('MindMapAdapter: No children to process');
      return;
    }

    // Create a temporary MBCP structure for the unified processor
    const tempMBCPData: MBCPData = {
      mindmap: {
        root: {
          id: parentId,
          text: 'Temporary Root',
          description: 'Temporary root for processing children',
          children: children
        }
      }
    };

    // Use the unified processor to handle the children
    // Note: This will process the children but we need to handle the parent relationship manually
    children.forEach((child, index) => {
      if (!child.text) {
        console.warn('MindMapAdapter: Child node missing text property, skipping');
        return;
      }

      // Get window dimensions for positioning
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;

      // Calculate position based on index and window size
      const horizontalSpacing = Math.min(250, windowWidth * 0.15);
      const verticalOffset = (index - Math.floor(children.length / 2)) * 100;
      
      const x = Math.max(100, Math.min(windowWidth / 2 + horizontalSpacing, windowWidth - 200));
      const y = Math.max(100, Math.min(windowHeight / 2 + verticalOffset, windowHeight - 100));

      // Get the parent node to determine the proper node path
      const parentNode = storeState.nodes[parentId];
      const parentPath = parentNode?.metadata?.nodePath || '1';

      // Create the node path
      const nodePath = parentPath.endsWith('.0')
        ? `${parentPath.slice(0, -2)}.${index + 1}`
        : `${parentPath}.${index + 1}`;

      console.log(`MindMapAdapter: Adding child node ${index + 1}: "${child.text}"`);

      // Add the child node using the store state directly
      const childId = storeState.addNode(
        parentId,
        child.text,
        x,
        y,
        {
          width: 180,
          height: 70,
          description: child.description || child.text,
          metadata: {
            nodePath: nodePath,
            intent: child.metadata?.intent || 'teleological',
            tags: child.metadata?.tags || [],
            creationSource: 'mbcp_adapter',
            ...child.metadata
          }
        }
      );

      console.log(`MindMapAdapter: Added child node with ID: ${childId}`);

      // Process grandchildren recursively
      if (childId && child.children && child.children.length > 0) {
        console.log(`MindMapAdapter: Processing ${child.children.length} grandchildren for node ${childId}`);
        processChildNodes(storeState, childId, child.children);
      }
    });

  } catch (error) {
    console.error('MindMapAdapter: Error processing child nodes:', error);
  }
};
