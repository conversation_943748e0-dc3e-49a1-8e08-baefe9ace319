/**
 * keyboardHandler.ts
 *
 * Global keyboard handler for the application.
 * This utility provides a centralized way to handle keyboard shortcuts.
 */

// Store import removed - global keyboard handler doesn't need store access
import RegistrationManager, { EventType } from '../core/services/RegistrationManager';

// Initialize the keyboard handler
export const initKeyboardHandler = () => {
  console.log('Initializing global keyboard handler');

  // Handle keydown events
  const handleKeyDown = (e: KeyboardEvent) => {
    // Skip if focus is in an input or textarea
    if (
      e.target instanceof HTMLInputElement ||
      e.target instanceof HTMLTextAreaElement
    ) {
      return;
    }

    console.log('Global key handler:', e.key, 'Shift:', e.shiftKey);

    // DISABLED: All Tab key handling moved to MindMapCanvas and KeyboardManager to prevent conflicts
    // This global handler is now primarily for logging and debugging
    if (e.key === 'Tab') {
      console.log('keyboardHandler.ts: Tab key detected but delegating to MindMapCanvas/KeyboardManager handlers');
      // Let the event bubble up to the appropriate handlers
      return;
    }
  };

  // Add the event listener
  window.addEventListener('keydown', handleKeyDown, true);

  // Return a cleanup function
  return () => {
    window.removeEventListener('keydown', handleKeyDown, true);
  };
};
