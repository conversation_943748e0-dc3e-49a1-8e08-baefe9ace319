/**
 * MBCPProcessor.ts
 *
 * Utility for processing MBCP (MindBack Content Protocol) data
 * and creating mindmaps from it.
 */

import { getMindMapStore } from '../../../core/state/MindMapStoreFactory';
import { v4 as uuidv4 } from 'uuid';

// MBCP data type (simplified)
interface MBCPNode {
  text: string;
  description?: string;
  intent?: string;
  metadata?: any;
  children?: MBCPNode[];
}

interface MBCPData {
  root?: MBCPNode;
  mindmap?: {
    root: MBCPNode;
  };
  text?: string;
  description?: string;
  intent?: string;
  children?: MBCPNode[];
  full_text?: string;
  suggestedActions?: Array<{
    type: string;
    label?: string;
    data?: any;
  }>;
}

/**
 * Create a mind map from MBCP data.
 *
 * @param mbcpData The MBCP data to process
 * @param sheetId The sheet ID for the mindmap store
 * @returns True if the mind map was created successfully, false otherwise
 */
export const createMindmapFromMBCP = (mbcpData: MBCPData, sheetId: string): boolean => {
  try {
    console.log('MBCPProcessor: Creating mindmap from MBCP data', JSON.stringify(mbcpData, null, 2));

    // Validate the MBCP data structure
    if (!mbcpData) {
      console.error('MBCPProcessor: Invalid MBCP data - data is null or undefined');
      return false;
    }

    // Get the sheet-specific MindMap store
    const store = getMindMapStore(sheetId).getState();

    // Check for nested MBCP data in suggestedActions
    if (mbcpData.suggestedActions && mbcpData.suggestedActions.length > 0) {
      const createMindmapAction = mbcpData.suggestedActions.find(action => action.type === 'create_mindmap');
      if (createMindmapAction && createMindmapAction.data && createMindmapAction.data.mbcpData) {
        console.log('MBCPProcessor: Found nested MBCP data in suggestedActions');

        // If the nested data has a mindmap structure, use it
        if (createMindmapAction.data.mbcpData.mindmap) {
          console.log('MBCPProcessor: Using nested mindmap structure');

          // Check for placeholder variables and reject if found
          const nestedMindmap = createMindmapAction.data.mbcpData.mindmap;
          if (nestedMindmap.root) {
            // Check for placeholder variables in text
            if (nestedMindmap.root.text && (nestedMindmap.root.text.includes('{topic}') || nestedMindmap.root.text.includes('{g-llm_dialogue}'))) {
              console.error('MBCPProcessor: Placeholder variables found in root node text');
              alert('Error: The mindmap data contains placeholder variables. This indicates a backend issue.');
              return false;
            }

            // Check for placeholder variables in description
            if (nestedMindmap.root.description && (nestedMindmap.root.description.includes('{topic}') || nestedMindmap.root.description.includes('{g-llm_dialogue}'))) {
              console.error('MBCPProcessor: Placeholder variables found in root node description');
              alert('Error: The mindmap data contains placeholder variables. This indicates a backend issue.');
              return false;
            }

            // Use this nested structure
            mbcpData = {
              ...mbcpData,
              mindmap: nestedMindmap
            };
          }
        }
      }
    }

    // Determine the root node data based on the structure - only accept proper mindmap structure
    let rootNodeData: MBCPNode;

    if (mbcpData.mindmap && mbcpData.mindmap.root) {
      console.log('MBCPProcessor: Processing complete MBCP data with mindmap structure');
      rootNodeData = mbcpData.mindmap.root;
    } else {
      console.error('MBCPProcessor: Invalid MBCP data structure - missing mindmap.root', mbcpData);
      // Continue with available data instead of showing an error
      return false;
    }

    // Check if children are provided - but don't show an error if none exist
    if (!rootNodeData.children || rootNodeData.children.length === 0) {
      console.log('MBCPProcessor: No children found in MBCP data - this is now acceptable');
      // We'll continue with just the root node
      rootNodeData.children = [];
    }

    // Log the structure we're working with
    console.log('MBCPProcessor: Root node data:', rootNodeData);
    console.log('MBCPProcessor: Children count:', rootNodeData.children?.length || 0);

    // Create a new project with the root node text
    const projectName = rootNodeData.text || 'Generated Mind Map';
    console.log('MBCPProcessor: Creating new project with name:', projectName);

    // First check if there are existing nodes
    const existingNodes = Object.keys(store.nodes).length;
    console.log('MBCPProcessor: Existing nodes count:', existingNodes);

    // Only initialize if there are no existing nodes
    if (existingNodes > 0) {
      console.log('MBCPProcessor: Clearing existing nodes before creating new mindmap');
    }

    // Clear existing nodes first to ensure a clean slate
    store.initialize(window.innerWidth, window.innerHeight);

    // Get the root node ID directly from the createNewProject function
    const rootNodeId = store.createNewProject(projectName);
    console.log('MBCPProcessor: Root node ID after project creation:', rootNodeId);

    if (!rootNodeId) {
      console.error('MBCPProcessor: Failed to create root node');
      return false;
    }

    // Update the root node with the MBCP data
    // Get a fresh reference to the store to ensure we have the latest state
    const freshStore = getMindMapStore(sheetId).getState();
    const rootNode = freshStore.nodes[rootNodeId];

    if (rootNode) {
      console.log('MBCPProcessor: Found root node in store, updating with MBCP data');
      freshStore.updateNode(rootNodeId, {
        text: rootNodeData.text,
        description: rootNodeData.description || rootNodeData.text,
        metadata: {
          ...rootNode.metadata,
          intent: rootNodeData.intent || 'teleological',
          ...(rootNodeData.metadata || {})
        }
      });
      console.log('MBCPProcessor: Updated root node via updateNode method');
    } else {
      // If we can't find the node in the store, try again with a small delay
      console.warn('MBCPProcessor: Root node not immediately found in store, trying with delay');

      // FIXED: Get store reference outside of setTimeout to avoid hook violations
      const delayedStore = getMindMapStore(sheetId).getState();

      // Process synchronously with a small delay to ensure store is updated
      setTimeout(() => {
        const delayedRootNode = delayedStore.nodes[rootNodeId];

        if (delayedRootNode) {
          console.log('MBCPProcessor: Found root node, updating with MBCP data');
          delayedStore.updateNode(rootNodeId, {
            text: rootNodeData.text,
            description: rootNodeData.description || rootNodeData.text,
            metadata: {
              ...(delayedRootNode.metadata || {}),
              intent: rootNodeData.intent || 'teleological',
              ...(rootNodeData.metadata || {})
            }
          });
          console.log('MBCPProcessor: Updated root node via updateNode method');

          // Process children if any
          if (rootNodeData.children && rootNodeData.children.length > 0) {
            console.log('MBCPProcessor: Processing children');
            processChildren(rootNodeId, rootNodeData.children);
          }

          // CONSOLIDATED: Apply layout through governance-integrated method
          if (delayedStore.updateLayout) {
            console.log('MBCPProcessor: Requesting layout through UnifiedLayoutManager');

            // Use the governance-integrated updateLayout method (async)
            delayedStore.updateLayout('topDown', 'system').then((success) => {
              if (success) {
                console.log('MBCPProcessor: Layout applied successfully via UnifiedLayoutManager');
              } else {
                console.warn('MBCPProcessor: Layout request was rejected by governance');
              }
            }).catch(error => {
              console.error('MBCPProcessor: Error applying layout:', error);
            });
          }
        } else {
          console.error('MBCPProcessor: Root node not found in store');
        }
      }, 50); // Small delay to ensure store is updated

      // Return true since we're handling the update asynchronously
      return true;
    }

    // Only process children and update layout if we found the root node immediately
    // Otherwise, these operations are handled in the setTimeout callback
    if (rootNode) {
      // Process children recursively
      if (rootNodeData.children && rootNodeData.children.length > 0) {
        console.log('MBCPProcessor: Processing', rootNodeData.children.length, 'children');
        processChildren(rootNodeId, rootNodeData.children);
      } else {
        console.log('MBCPProcessor: No children to process');
      }

      // Apply layout if available with a small delay to ensure all nodes are processed
      setTimeout(() => {
        const layoutStore = useMindMapStore.getState();
        if (layoutStore.updateLayout) {
          console.log('MBCPProcessor: Updating layout with delay');
          layoutStore.updateLayout('topDown');
        }
      }, 50);
    }

    console.log('MBCPProcessor: Mindmap creation complete');
    return true;
  } catch (error) {
    console.error('MBCPProcessor: Error creating mind map from MBCP data:', error);
    return false;
  }
};

/**
 * Process children nodes recursively.
 *
 * @param parentId The ID of the parent node
 * @param children The children nodes to process
 */
const processChildren = (parentId: string, children: MBCPNode[]): void => {
  // Get the MindMap store
  const store = useMindMapStore.getState();

  // Process each child
  children.forEach((child, index) => {
    if (!child.text) {
      console.warn('MBCPProcessor: Child node missing text property', child);
      return;
    }

    // Use parent position plus offset for better initial layout
    // Get the parent node position
    const parentNode = store.nodes[parentId];
    const parentX = parentNode?.x || 0;
    const parentY = parentNode?.y || 0;

    // Add offset based on index to prevent overlapping
    const x = parentX + 200; // Move right from parent
    const y = parentY + (index * 100) - ((children.length - 1) * 50); // Distribute vertically

    // Get the parent node path
    const parentPath = parentNode?.metadata?.nodePath || '1';

    // Create the node path (e.g., 1.1, 1.2, etc.)
    const nodePath = parentPath.endsWith('.0')
      ? `${parentPath.slice(0, -2)}.${index + 1}`
      : `${parentPath}.${index + 1}`;

    // Use the original node text without adding an index prefix
    // This prevents the duplicate "1." issue in node titles
    let nodeText = child.text;

    console.log(`MBCPProcessor: Adding child node ${index} at position (${x}, ${y}) with path ${nodePath}`);

    // Add the child node with improved dimensions
    const childId = store.addNode(
      parentId,
      nodeText,
      x,
      y,
      {
        width: 180, // Wider nodes for better text display
        height: 70, // Standard height for nodes
        description: child.description || child.text,
        metadata: {
          nodePath: nodePath,
          intent: child.intent || 'teleological',
          ...(child.metadata || {})
        }
      }
    );

    // Process grandchildren recursively
    if (childId && child.children && child.children.length > 0) {
      console.log(`MBCPProcessor: Processing ${child.children.length} grandchildren for node ${childId}`);
      processChildren(childId, child.children);
    }
  });
};

// Removed generateChildrenFromText function as it was a workaround
