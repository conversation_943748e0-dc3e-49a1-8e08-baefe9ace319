/**
 * AppWorkingState.tsx
 * 
 * Working state component that uses context-based store access for persistence operations.
 * This component is wrapped by StoreProvider and has access to all store contexts.
 */

import React, { useEffect, useCallback } from 'react';
import { Routes, Route } from 'react-router-dom';
import { MindBook } from '../features/mindsheet';
import GovernanceBoxPositioned from '../governance/chat/GovernanceBoxPositioned';
import { processGovernanceAction } from '../core/adapters/GovernanceMindBookAdapter';
import MindSheetTabs from '../features/mindsheet/MindSheetTabs';
import ProjectDialog from '../features/mindmap/components/Dialogs/ProjectDialog';
// Legacy global MindMapStore import removed - using sheet-specific stores via MindBook framework
import FooterContextButton from '../features/context/components/FooterContextButton';
import { useMindBookPersistence } from '../core/hooks/useMindBookPersistence';
import { useMindBookStoreContext } from '../core/context/StoreContexts';
import { LogoAssets } from '../utils/assetPaths';

interface AppWorkingStateProps {
  // State props
  showGovernanceChat: boolean;
  isGovernanceChatCollapsed: boolean;
  isGovernanceChatFullyCollapsed: boolean;
  isContextPanelOpen: boolean;
  headerLogoRetries: number;
  
  // Event handlers
  onGovernanceClose: () => void;
  onGovernanceCollapse: () => void;
  onContextPanelToggle: () => void;
  onLogoClick: () => void;
  onShowProjectDialog: () => void;
  onCloseProjectDialog: () => void;
  onHeaderLogoRetry: () => void;
}

const AppWorkingState: React.FC<AppWorkingStateProps> = ({
  showGovernanceChat,
  isGovernanceChatCollapsed,
  isGovernanceChatFullyCollapsed,
  isContextPanelOpen,
  headerLogoRetries,
  onGovernanceClose,
  onGovernanceCollapse,
  onContextPanelToggle,
  onLogoClick,
  onShowProjectDialog,
  onCloseProjectDialog,
  onHeaderLogoRetry
}) => {
  // Get context-based persistence operations
  const { autoSaveSession, stores } = useMindBookPersistence();
  const mindBookStore = useMindBookStoreContext();
  
  // Project dialog state now managed by MindBook framework - no global store needed

  // Logo configuration
  const logoConfig = LogoAssets.getPrimaryLogo((e) => {
    if (headerLogoRetries < 2) {
      onHeaderLogoRetry();
      console.log(`AppWorkingState: Logo load failed, retry ${headerLogoRetries + 1} of 2`);
    }
  });

  // Set up auto-save subscription using context-based store access
  useEffect(() => {
    console.log('AppWorkingState: Setting up MindBook store subscription for auto-save');

    const unsubscribe = mindBookStore.subscribe?.((state) => {
      // Only auto-save if we have sheets
      if (state.sheets.length > 0) {
        console.log('AppWorkingState: MindBook state changed, auto-saving session...');
        try {
          const success = autoSaveSession();
          if (success) {
            console.log('AppWorkingState: ✅ Auto-save successful');
          } else {
            console.warn('AppWorkingState: ❌ Auto-save failed');
          }
        } catch (error) {
          console.error('AppWorkingState: Auto-save error:', error);
        }
      }
    });

    return unsubscribe;
  }, [autoSaveSession, mindBookStore]);

  // Save session before page unload using context-based persistence
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      try {
        // Immediate auto-save before page unload using context-based persistence
        const success = autoSaveSession();
        if (success) {
          console.log('AppWorkingState: Auto-saved before page unload');
        } else {
          console.warn('AppWorkingState: Failed to auto-save before page unload');
        }
      } catch (error) {
        console.error('AppWorkingState: Error saving before unload:', error);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [autoSaveSession]);

  // Handle governance action
  const handleGovernanceAction = useCallback((action: any) => {
    console.log('Governance action:', action);

    // Process the action using the adapter
    const success = processGovernanceAction(action);

    console.log('Action processed successfully:', success);

    return success;
  }, []);

  // Project dialog handlers
  const handleShowProjectDialog = useCallback(() => {
    setShowProjectDialog(true);
    onShowProjectDialog();
  }, [setShowProjectDialog, onShowProjectDialog]);

  const handleCloseProjectDialog = useCallback(() => {
    setShowProjectDialog(false);
    onCloseProjectDialog();
  }, [setShowProjectDialog, onCloseProjectDialog]);

  return (
    <div className="app-container">
      {/* Header - restored to original design */}
      <header className="app-header">
        <div className="logo-container">
          <img
            src={logoConfig.src}
            alt="MindBack Logo"
            className="app-header-logo"
            onClick={onLogoClick}
            style={{ cursor: 'pointer' }}
            title="Return to Home"
            onError={logoConfig.onError}
            onLoad={() => {
              console.log('AppWorkingState: Header logo loaded successfully');
            }}
          />
          <span className="logo-slogan"> mindback.ai - intelligence moderation</span>
        </div>
        <div className="header-controls">
          {/* Hamburger Menu button - opens project management */}
          <button
            className="menu-button"
            onClick={handleShowProjectDialog}
            title="Project Management"
          >
            ☰
          </button>
          {/* Help button */}
          <button
            className="help-button" 
            title="Help"
          >
            ?
          </button>
        </div>
      </header>

      {/* Project Management Dialog */}
      {showProjectDialog && (
        <ProjectDialog isOpen={showProjectDialog} onClose={handleCloseProjectDialog} />
      )}

      {/* Main content area */}
      <div className="app-content">
        <Routes>
          <Route path="*" element={
            <MindBook
              governanceComponent={
                <GovernanceBoxPositioned
                  isOpen={showGovernanceChat && !isGovernanceChatFullyCollapsed}
                  isCollapsed={isGovernanceChatCollapsed}
                  onClose={onGovernanceClose}
                  onCollapse={onGovernanceCollapse}
                  onAction={handleGovernanceAction}
                  isContextPanelOpen={isContextPanelOpen}
                />
              }
            />
          } />
        </Routes>
      </div>

      {/* MindSheet Tabs - positioned via CSS */}
      <MindSheetTabs />

      {/* Footer - black with centered mindback.ai and functional context button */}
      <footer className="app-footer">
        <div className="session-info" style={{ display: 'flex', alignItems: 'center', maxWidth: '300px' }}>
          <FooterContextButton
            isOpen={isContextPanelOpen}
            onClick={onContextPanelToggle}
          />
          {mindBookStore.sheets.length > 0 && (
            <span style={{
              color: '#ffffff',
              fontSize: '12px',
              marginLeft: '10px',
              opacity: 0.7
            }}>
              {/* Show MindBook name if available, otherwise show session info */}
              {mindBookStore.name ? (
                <>
                  <strong>{mindBookStore.name}</strong>
                  <span style={{ opacity: 0.5, marginLeft: '8px' }}>
                    ({mindBookStore.sheets.length} sheet{mindBookStore.sheets.length !== 1 ? 's' : ''})
                  </span>
                </>
              ) : (
                `Session: ${mindBookStore.sheets.length} sheet${mindBookStore.sheets.length !== 1 ? 's' : ''}`
              )}
            </span>
          )}
        </div>
        
        <div style={{ 
          color: '#ffffff', 
          fontSize: '14px',
          fontWeight: '400',
          textAlign: 'center',
          flex: 1
        }}>
          mindback.ai
        </div>
        
        <div style={{ width: '300px' }}></div>
      </footer>
    </div>
  );
};

export default AppWorkingState;
