import { useChatForkStore, TrackedSelection } from '../../core/state/ChatForkStore';
import { ChatResponse } from '../../../../services/api/GovernanceLLM';
// Legacy MindMapStore import removed - using sheet-specific stores via MindBook framework

/**
 * ChatForkAdapter provides integration between UI components and ChatForkStore
 */
export class ChatForkAdapter {
  // Keep track of selections locally to avoid store compatibility issues
  private static activeSelections: Map<string, TrackedSelection> = new Map();
  
  /**
   * Generate a unique ID for selections
   */
  private static generateUniqueId(): string {
    return 'sel-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Initialize the global Ctrl+Tab handler
   * This should be called once during application initialization
   */
  static initializeGlobalKeyHandler(): void {
    console.log('ChatForkAdapter - Initializing global key handler');
    
    // Remove any existing handler first to prevent duplicates
    document.removeEventListener('keydown', this.handleCtrlTab);
    
    // Add the Ctrl+Tab handler
    document.addEventListener('keydown', this.handleCtrlTab);
    console.log('ChatForkAdapter - Global key handler initialized successfully');
  }

  /**
   * Handle Ctrl+Tab key combination to create sticky selection
   */
  private static handleCtrlTab = (e: KeyboardEvent): void => {
    // Check if Ctrl+Tab was pressed
    console.log('ChatForkAdapter - Key pressed:', e.key, 'Ctrl:', e.ctrlKey);
    
    if (e.ctrlKey && e.key === 'Tab') {
      console.log('ChatForkAdapter - Ctrl+Tab detected!');
      e.preventDefault(); // Prevent default tab behavior
      
      // Get the current selection
      const selection = window.getSelection();
      console.log('ChatForkAdapter - Current selection:', selection?.toString());
      
      if (!selection || selection.toString().trim() === '') {
        console.log('ChatForkAdapter - No text selected');
        return;
      }
      
      // Create a tracked selection
      this.createTrackedSelection(selection, 'default');
    }
  };

  /**
   * Create a tracked selection from a browser selection
   */
  static createTrackedSelection(selection: Selection, forkId: string): TrackedSelection | null {
    if (!selection || selection.toString().trim() === '') return null;
    
    const selectedText = selection.toString().trim();
    console.log('ChatForkAdapter - Creating tracked selection:', selectedText);
    
    // Create a unique ID for this selection
    const selectionId = this.generateUniqueId();
    
    // Create the tracked selection object
    const trackedSelection: TrackedSelection = {
      id: selectionId,
      text: selectedText,
      forkId: forkId,
      timestamp: Date.now()
    };
    
    // Apply visual highlight to the DOM
    this.applyHighlightToSelection(selection, selectionId, forkId);
    
    // Store the selection locally
    this.activeSelections.set(selectionId, trackedSelection);
    
    // Also store in the global store if it's available
    try {
      const store = useChatForkStore.getState();
      if (store && typeof store.addSelection === 'function') {
        store.addSelection(trackedSelection);
      }
    } catch (error) {
      console.error('ChatForkAdapter - Error adding to store:', error);
    }
    
    // Clear the browser selection after creating our tracked selection
    selection.removeAllRanges();
    
    return trackedSelection;
  }

  /**
   * Apply highlight to the selected text in the DOM
   */
  private static applyHighlightToSelection(selection: Selection, selectionId: string, forkId: string): void {
    if (!selection.rangeCount) return;
    
    console.log('ChatForkAdapter - Applying highlight to selection');
    
    try {
      // Get the current range
      const range = selection.getRangeAt(0);
      
      // Create a span element to highlight the selection
      const highlightEl = document.createElement('span');
      highlightEl.className = 'tracked-selection';
      highlightEl.dataset.selectionId = selectionId;
      highlightEl.dataset.forkId = forkId;
      
      // Clone the range contents to preserve it
      const contents = range.cloneContents();
      highlightEl.appendChild(contents);
      
      // Clear the original content and insert our highlighted version
      range.deleteContents();
      range.insertNode(highlightEl);
      
      console.log('ChatForkAdapter - Highlight applied successfully');
    } catch (error) {
      console.error('ChatForkAdapter - Error applying highlight:', error);
      
      // Fallback method - direct text replacement
      try {
        const allTextNodes = this.getAllTextNodes(document.body);
        const selectedText = selection.toString();
        
        // Find a text node containing our selection
        for (const textNode of allTextNodes) {
          const nodeText = textNode.textContent || '';
          const index = nodeText.indexOf(selectedText);
          
          if (index >= 0) {
            // Found a match, split the text node and insert our highlight
            const beforeText = nodeText.substring(0, index);
            const afterText = nodeText.substring(index + selectedText.length);
            
            const parentNode = textNode.parentNode;
            if (!parentNode) continue;
            
            // Create new text nodes and highlight
            const beforeNode = document.createTextNode(beforeText);
            const afterNode = document.createTextNode(afterText);
            
            const highlightEl = document.createElement('span');
            highlightEl.className = 'tracked-selection';
            highlightEl.dataset.selectionId = selectionId;
            highlightEl.dataset.forkId = forkId;
            highlightEl.textContent = selectedText;
            
            // Replace the original with our three new nodes
            const fragment = document.createDocumentFragment();
            fragment.appendChild(beforeNode);
            fragment.appendChild(highlightEl);
            fragment.appendChild(afterNode);
            
            parentNode.replaceChild(fragment, textNode);
            console.log('ChatForkAdapter - Highlight applied with fallback method');
            return;
          }
        }
      } catch (fallbackError) {
        console.error('ChatForkAdapter - Fallback highlight also failed:', fallbackError);
      }
    }
  }
  
  /**
   * Get all text nodes in the document
   */
  private static getAllTextNodes(node: Node): Node[] {
    const textNodes: Node[] = [];
    
    const walk = document.createTreeWalker(
      node,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: function(node) {
          // Skip empty text nodes
          if (!node.textContent?.trim()) {
            return NodeFilter.FILTER_REJECT;
          }
          return NodeFilter.FILTER_ACCEPT;
        }
      }
    );
    
    let currentNode;
    while (currentNode = walk.nextNode()) {
      textNodes.push(currentNode);
    }
    
    return textNodes;
  }

  /**
   * Remove a specific selection by ID
   */
  static removeSelection(selectionId: string): void {
    console.log('ChatForkAdapter - Removing selection:', selectionId);
    
    // Remove from local storage
    this.activeSelections.delete(selectionId);
    
    // Also remove from store if available
    try {
      const store = useChatForkStore.getState();
      if (store && typeof store.removeSelection === 'function') {
        store.removeSelection(selectionId);
      }
    } catch (error) {
      console.error('ChatForkAdapter - Error removing from store:', error);
    }
    
    // Remove the highlight from the DOM
    const highlightEl = document.querySelector(`[data-selection-id="${selectionId}"]`);
    if (highlightEl && highlightEl.parentNode) {
      // Replace the highlight with its text content
      const textNode = document.createTextNode(highlightEl.textContent || '');
      highlightEl.parentNode.replaceChild(textNode, highlightEl);
    }
  }

  /**
   * Clear all selections
   */
  static clearAllSelections(): void {
    console.log('ChatForkAdapter - Clearing all selections');
    
    // Clear local storage
    this.activeSelections.clear();
    
    // Also clear store if available
    try {
      const store = useChatForkStore.getState();
      if (store && typeof store.clearAllSelections === 'function') {
        store.clearAllSelections();
      }
    } catch (error) {
      console.error('ChatForkAdapter - Error clearing store selections:', error);
    }
    
    // Remove all highlights from the DOM
    document.querySelectorAll('.tracked-selection').forEach(el => {
      if (el.parentNode) {
        const textNode = document.createTextNode(el.textContent || '');
        el.parentNode.replaceChild(textNode, el);
      }
    });
  }

  /**
   * Hide the ChatFork
   */
  static hideChatFork(): void {
    console.log('ChatForkAdapter - Hiding ChatFork');
    
    try {
      const store = useChatForkStore.getState();
      if (store) {
        store.hideChatFork();
      }
    } catch (error) {
      console.error('ChatForkAdapter - Error hiding ChatFork:', error);
    }
  }

  /**
   * Create a fork node from a specific selection
   */
  static createForkFromSelection(selectionId?: string): string | null {
    console.log('ChatForkAdapter - Creating fork from selection:', selectionId);
    
    let selectedText: string | null = null;
    
    // First try to get the selection from our local storage
    if (selectionId) {
      const selection = this.activeSelections.get(selectionId);
      if (selection) {
        selectedText = selection.text;
      }
    } else {
      // Use the first available selection
      const firstSelection = Array.from(this.activeSelections.values())[0];
      if (firstSelection) {
        selectedText = firstSelection.text;
        selectionId = firstSelection.id;
      }
    }
    
    // If we found a selection, remove it and return success
    if (selectedText && selectionId) {
      this.removeSelection(selectionId);
      
      // Create a simple alert to show we processed it
      alert(`Created fork for: "${selectedText.substring(0, 40)}..."`);
      
      return "new-node-id";
    }
    
    console.warn('ChatForkAdapter - Cannot create fork: no selected text');
    return null;
  }
}