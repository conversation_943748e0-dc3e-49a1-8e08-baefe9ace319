/**
 * MBCPProcessorFix.ts
 *
 * Enhanced utility for processing MBCP (MindBack Content Protocol) data
 * and creating mindmaps from it. This version handles nested MBCP structures
 * and strictly enforces proper MBCP structure without fallbacks or workarounds.
 */

import { getMindMapStore } from '../../../core/state/MindMapStoreFactory';
import { v4 as uuidv4 } from 'uuid';

// MBCP data type (simplified)
interface MBCPNode {
  text: string;
  description?: string;
  intent?: string;
  metadata?: any;
  children?: MBCPNode[];
}

interface MBCPData {
  root?: MBCPNode;
  mindmap?: {
    root: MBCPNode;
  };
  text?: string;
  description?: string;
  intent?: string;
  children?: MBCPNode[];
  suggestedActions?: Array<{
    type: string;
    label: string;
    data: {
      mbcpData?: any;
    };
  }>;
}

/**
 * Create a mind map from MBCP data.
 *
 * @param mbcpData The MBCP data to process
 * @param sheetId The sheet ID for the mindmap store
 * @returns True if the mind map was created successfully, false otherwise
 */
export const createMindmapFromMBCP = (mbcpData: MBCPData, sheetId: string): boolean => {
  try {
    console.log('MBCPProcessorFix: Creating mindmap from MBCP data', JSON.stringify(mbcpData, null, 2));

    // Get the sheet-specific MindMap store
    const store = getMindMapStore(sheetId).getState();

    // Check for nested MBCP data in suggestedActions
    if (mbcpData.suggestedActions && mbcpData.suggestedActions.length > 0) {
      const createMindmapAction = mbcpData.suggestedActions.find(action => action.type === 'create_mindmap');
      if (createMindmapAction && createMindmapAction.data && createMindmapAction.data.mbcpData) {
        console.log('MBCPProcessorFix: Found nested MBCP data in suggestedActions');

        // If the nested data has a mindmap structure, use it
        if (createMindmapAction.data.mbcpData.mindmap) {
          console.log('MBCPProcessorFix: Using nested mindmap structure');

          // Check for placeholder variables and reject if found
          const nestedMindmap = createMindmapAction.data.mbcpData.mindmap;
          if (nestedMindmap.root) {
            // Check for placeholder variables in text
            if (nestedMindmap.root.text && (nestedMindmap.root.text.includes('{topic}') || nestedMindmap.root.text.includes('{g-llm_dialogue}'))) {
              console.error('MBCPProcessorFix: Placeholder variables found in root node text');
              alert('Error: The mindmap data contains placeholder variables. This indicates a backend issue.');
              return false;
            }

            // Check for placeholder variables in description
            if (nestedMindmap.root.description && (nestedMindmap.root.description.includes('{topic}') || nestedMindmap.root.description.includes('{g-llm_dialogue}'))) {
              console.error('MBCPProcessorFix: Placeholder variables found in root node description');
              alert('Error: The mindmap data contains placeholder variables. This indicates a backend issue.');
              return false;
            }

            // Use this nested structure
            mbcpData = {
              ...mbcpData,
              mindmap: nestedMindmap
            };
          }
        }
      }
    }

    // Determine the root node data based on the structure - only accept proper mindmap structure
    let rootNodeData: MBCPNode;

    if (mbcpData.mindmap && mbcpData.mindmap.root) {
      console.log('MBCPProcessorFix: Processing complete MBCP data with mindmap structure');
      rootNodeData = mbcpData.mindmap.root;
    } else {
      console.error('MBCPProcessorFix: Invalid MBCP data structure - missing mindmap.root', mbcpData);
      // Continue with available data instead of showing an error
      return false;
    }

    // Check if children are provided - but don't show an error if none exist
    if (!rootNodeData.children || rootNodeData.children.length === 0) {
      console.log('MBCPProcessorFix: No children found in MBCP data - this is now acceptable');
      // We'll continue with just the root node
      rootNodeData.children = [];
    }

    // Log the structure we're working with
    console.log('MBCPProcessorFix: Root node data:', rootNodeData);
    console.log('MBCPProcessorFix: Children count:', rootNodeData.children?.length || 0);

    // Create a new project with the root node text
    const projectName = rootNodeData.text || 'Generated Mind Map';
    console.log('MBCPProcessorFix: Creating new project with name:', projectName);

    // Clear existing nodes first to ensure a clean slate
    store.initialize(window.innerWidth, window.innerHeight);

    // Get the root node ID directly from the createNewProject function
    const rootNodeId = store.createNewProject(projectName);
    console.log('MBCPProcessorFix: Root node ID after project creation:', rootNodeId);

    if (!rootNodeId) {
      console.error('MBCPProcessorFix: Failed to create root node');
      return false;
    }

    // Update the root node with the MBCP data
    // Get a fresh reference to the store to ensure we have the latest state
    const freshStore = useMindMapStore.getState();
    const rootNode = freshStore.nodes[rootNodeId];

    if (rootNode) {
      console.log('MBCPProcessorFix: Found root node in store, updating with MBCP data');
      freshStore.updateNode(rootNodeId, {
        text: rootNodeData.text,
        description: rootNodeData.description || rootNodeData.text,
        metadata: {
          ...rootNode.metadata,
          intent: rootNodeData.intent || 'teleological',
          ...(rootNodeData.metadata || {})
        }
      });
      console.log('MBCPProcessorFix: Updated root node via updateNode method');

      // Process children recursively
      if (rootNodeData.children && rootNodeData.children.length > 0) {
        console.log('MBCPProcessorFix: Processing', rootNodeData.children.length, 'children');
        processChildren(rootNodeId, rootNodeData.children);
      } else {
        console.log('MBCPProcessorFix: No children to process');
      }

      // Apply layout if available
      if (freshStore.updateLayout) {
        console.log('MBCPProcessorFix: Updating layout');
        freshStore.updateLayout('topDown');

        // Force another layout update after a delay to ensure proper positioning
        setTimeout(() => {
          console.log('MBCPProcessorFix: Final layout update for immediate path');
          const finalStore = getMindMapStore(sheetId).getState();
          finalStore.updateLayout('topDown');

          // The canvas will handle centering the viewport
          // No need to set position here as it interferes with proper centering

          // Force a re-render by updating a state value
          finalStore.setScale(finalStore.scale);
        }, 500);
      }
    } else {
      // If the root node wasn't found immediately, try again after a short delay
      console.log('MBCPProcessorFix: Root node not found immediately, trying again after delay');

      // FIXED: Get store reference outside of setTimeout to avoid hook violations
      const delayedStore = useMindMapStore.getState();

      setTimeout(() => {
        const delayedRootNode = delayedStore.nodes[rootNodeId];

        if (delayedRootNode) {
          console.log('MBCPProcessorFix: Found root node after delay, updating with MBCP data');
          delayedStore.updateNode(rootNodeId, {
            text: rootNodeData.text,
            description: rootNodeData.description || rootNodeData.text,
            metadata: {
              ...(delayedRootNode.metadata || {}),
              intent: rootNodeData.intent || 'teleological',
              ...(rootNodeData.metadata || {})
            }
          });
          console.log('MBCPProcessorFix: Updated root node via updateNode method after delay');

          // Process children if any
          if (rootNodeData.children && rootNodeData.children.length > 0) {
            console.log('MBCPProcessorFix: Processing children after delay');
            processChildren(rootNodeId, rootNodeData.children);
          }

          // Apply layout
          if (delayedStore.updateLayout) {
            console.log('MBCPProcessorFix: Updating layout after delay');
            delayedStore.updateLayout('topDown');

            // FIXED: Get store reference outside of setTimeout to avoid hook violations
            const finalStore = getMindMapStore(sheetId).getState();

            // Force another layout update after a delay to ensure proper positioning
            setTimeout(() => {
              console.log('MBCPProcessorFix: Final layout update');
              finalStore.updateLayout('topDown');

              // The canvas will handle centering the viewport
              // No need to set position here as it interferes with proper centering

              // Force a re-render by updating a state value
              finalStore.setScale(finalStore.scale);
            }, 500);
          }
        } else {
          console.error('MBCPProcessorFix: Root node not found in store even after delay');
        }
      }, 300); // Increased delay to 300ms for better reliability
    }

    console.log('MBCPProcessorFix: Mindmap creation complete');
    return true;
  } catch (error) {
    console.error('MBCPProcessorFix: Error creating mind map from MBCP data:', error);
    return false;
  }
};

/**
 * Process children nodes recursively.
 *
 * @param parentId The ID of the parent node
 * @param children The children nodes to process
 */
const processChildren = (parentId: string, children: MBCPNode[]): void => {
  // Get the MindMap store
  const store = useMindMapStore.getState();

  // Process each child
  children.forEach((child, index) => {
    if (!child.text) {
      console.warn('MBCPProcessorFix: Child node missing text property', child);
      return;
    }

    // Use simple relative positioning - the layout algorithm will handle proper positioning
    // These are just temporary positions that will be overridden by the layout
    const x = (index - (children.length - 1) / 2) * 200;
    const y = 100; // Simple offset from parent

    // Get the parent node to determine the proper node path
    const parentNode = store.nodes[parentId];
    const parentPath = parentNode?.metadata?.nodePath || '1';

    // Create the node path (e.g., 1.1, 1.2, etc.)
    const nodePath = parentPath.endsWith('.0')
      ? `${parentPath.slice(0, -2)}.${index + 1}`
      : `${parentPath}.${index + 1}`;

    // Use the original node text without adding an index prefix
    let nodeText = child.text;

    console.log(`MBCPProcessorFix: Adding child node ${index} at position (${x}, ${y}) with path ${nodePath}`);

    // Add the child node
    const childId = store.addNode(
      parentId,
      nodeText,
      x,
      y,
      {
        description: child.description || child.text,
        metadata: {
          nodePath: nodePath,
          intent: child.intent || 'teleological',
          ...(child.metadata || {})
        }
      }
    );

    // Process grandchildren recursively
    if (childId && child.children && child.children.length > 0) {
      console.log(`MBCPProcessorFix: Processing ${child.children.length} grandchildren for node ${childId}`);
      processChildren(childId, child.children);
    }
  });
};

// Removed generateChildrenFromText function as it was a workaround
