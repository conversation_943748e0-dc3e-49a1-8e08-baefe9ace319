/**
 * MindMapToolbar.tsx
 *
 * Toolbar component for the mind map with controls for zoom, centering, etc.
 */

import React, { useState, useEffect } from 'react';
import { getMindMapStore } from '../../../../core/state/MindMapStoreFactory';
import './MindMapToolbar.css';

interface MindMapToolbarProps {
  sheetId: string;
}

const MindMapToolbar: React.FC<MindMapToolbarProps> = ({ sheetId }) => {
  // Get sheet-specific store
  const store = getMindMapStore(sheetId);
  const [storeState, setStoreState] = useState(() => store.getState());

  // Subscribe to store changes
  useEffect(() => {
    const unsubscribe = store.subscribe(setStoreState);
    return unsubscribe;
  }, [store]);

  // Handle zoom in
  const handleZoomIn = () => {
    storeState.setScale(storeState.scale * 1.2);
  };

  // Handle zoom out
  const handleZoomOut = () => {
    storeState.setScale(storeState.scale / 1.2);
  };

  // DISABLED: Competing centering system
  const handleCenter = () => {
    console.log('MindMapToolbar: 🚫 Centering DISABLED - preventing conflicts');
  };

  // Handle save
  const handleSave = () => {
    console.log('MindMapToolbar: Save functionality needs to be implemented in sheet-specific context');
    // Show a toast or notification here
  };

  return (
    <div className="mindmap-toolbar">
      <div className="toolbar-group">
        <button
          className="toolbar-button"
          onClick={handleZoomIn}
          title="Zoom In"
        >
          +
        </button>
        <button
          className="toolbar-button"
          onClick={handleZoomOut}
          title="Zoom Out"
        >
          -
        </button>
        <button
          className="toolbar-button"
          onClick={handleCenter}
          title="Center View"
        >
          ⌖
        </button>
      </div>

      <div className="toolbar-group">
        <button
          className="toolbar-button"
          onClick={handleSave}
          title="Save"
        >
          💾
        </button>
        <button
          className="toolbar-button"
          onClick={() => storeState.setShowProjectDialog(true)}
          title="Project"
        >
          📁
        </button>
      </div>
    </div>
  );
};

export default MindMapToolbar;
